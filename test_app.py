#!/usr/bin/env python3
"""
Quick test script to verify the Flask app is working correctly.
"""

print("Testing Flask app...")

try:
    from app import create_app
    print("✅ App module imported successfully")
    
    app = create_app()
    print("✅ App created successfully")
    
    print("\n📋 Registered Routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.endpoint}: {rule}")
    
    print("\n🚀 Starting Flask development server...")
    app.run(debug=True, host='127.0.0.1', port=5000)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
