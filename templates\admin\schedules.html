{% extends "base.html" %}

{% block title %}Class Schedule Management - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
.schedule-management-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1em;
}

.schedule-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin-bottom: 2em;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1.5em;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0 0 0.5em 0;
    font-size: 2em;
    color: var(--primary);
}

.stat-card p {
    margin: 0;
    color: var(--text-secondary);
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1em;
    margin-bottom: 2em;
}

.day-column {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
}

.day-header {
    background: var(--primary);
    color: white;
    padding: 1em;
    text-align: center;
}

.day-header h3 {
    margin: 0;
    font-size: 1.1em;
}

.day-classes {
    padding: 1em;
    min-height: 400px;
}

.class-card {
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: 6px;
    padding: 0.75em;
    margin-bottom: 0.5em;
    transition: all 0.2s ease;
}

.class-card:hover {
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.class-time {
    font-weight: bold;
    color: var(--primary);
    font-size: 0.9em;
}

.class-subject {
    font-weight: bold;
    margin: 0.25em 0;
}

.class-teacher {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.class-room {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.class-students {
    color: var(--success);
    font-size: 0.8em;
    margin-top: 0.25em;
}

.no-classes {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2em 0;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

@media (max-width: 768px) {
    .schedule-grid {
        grid-template-columns: 1fr;
    }
    
    .schedule-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .header-actions {
        flex-direction: column;
        gap: 0.5em;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="schedule-management-container">
    <div class="dashboard-header">
        <h1>📅 Class Schedule Management</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                ← Back to Dashboard
            </a>
        </div>
    </div>

    <div class="schedule-stats">
        <div class="stat-card">
            <h3>{{ total_classes }}</h3>
            <p>Total Classes</p>
        </div>
        <div class="stat-card">
            <h3>{{ schedule.values() | sum(attribute='__len__') }}</h3>
            <p>Scheduled Classes</p>
        </div>
        <div class="stat-card">
            <h3>5</h3>
            <p>School Days</p>
        </div>
        <div class="stat-card">
            <h3>{{ (schedule.values() | sum(attribute='__len__') / 5) | round(1) }}</h3>
            <p>Avg Classes/Day</p>
        </div>
    </div>

    {% if total_classes > 0 %}
        <div class="schedule-grid">
            {% set days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] %}
            {% for day in days %}
                <div class="day-column">
                    <div class="day-header">
                        <h3>{{ day }}</h3>
                    </div>
                    <div class="day-classes">
                        {% if schedule.get(day) %}
                            {% for class in schedule[day] %}
                                <div class="class-card">
                                    <div class="class-time">{{ class.time }}</div>
                                    <div class="class-subject">{{ class.subject }}</div>
                                    <div class="class-teacher">👨‍🏫 {{ class.teacher }}</div>
                                    <div class="class-room">🏫 Room {{ class.room }}</div>
                                    <div class="class-students">👥 {{ class.student_count }} students</div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="no-classes">
                                <p>No classes scheduled</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">📅</div>
            <h3>No Classes Scheduled</h3>
            <p>There are no classes scheduled yet. Teachers can create classes to populate the schedule.</p>
        </div>
    {% endif %}

    <div class="dashboard-actions">
        <h2>Schedule Management Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('admin.users') }}" class="btn btn-primary">👥 Manage Users</a>
            <a href="{{ url_for('admin.system_overview') }}" class="btn btn-info">📊 System Overview</a>
            <a href="{{ url_for('admin.notifications') }}" class="btn">📢 Notifications</a>
        </div>
    </div>
</div>
{% endblock %}
