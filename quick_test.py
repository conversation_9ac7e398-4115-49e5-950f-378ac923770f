#!/usr/bin/env python3
"""
Quick test to verify the duplicate route issue is fixed.
"""

print("🔍 Testing app creation after fixing duplicate routes...")

try:
    from app import create_app
    print("✅ create_app imported")
    
    app = create_app()
    print("✅ App created successfully!")
    
    # Count routes
    routes = list(app.url_map.iter_rules())
    print(f"✅ {len(routes)} routes registered")
    
    # Check for Phase 3 routes
    phase3_routes = ['/teacher/manage_classes', '/admin/notifications', '/admin/overview']
    for route in phase3_routes:
        found = any(str(rule).endswith(route) for rule in routes)
        if found:
            print(f"✅ Phase 3 route found: {route}")
        else:
            print(f"❌ Phase 3 route missing: {route}")
    
    print("\n🚀 App is ready to start!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
