{% extends "base.html" %}

{% block title %}Parent Registration - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <form method="POST" class="login-form">
        {{ form.hidden_tag() }}
        <div class="role-header parent-header">
            <div class="role-icon">👨‍👩‍👧‍👦</div>
            <h2>Parent Registration</h2>
            <p class="form-description">Register as a parent. Your account will need approval from an administrator.</p>
        </div>

        <div class="form-row">
            <div class="form-group">
                {{ form.first_name.label(class="form-label") }}
                {{ form.first_name(class="form-control", placeholder="Enter your first name") }}
                {% if form.first_name.errors %}
                    <div class="form-error">
                        {% for error in form.first_name.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.last_name.label(class="form-label") }}
                {{ form.last_name(class="form-control", placeholder="Enter your last name") }}
                {% if form.last_name.errors %}
                    <div class="form-error">
                        {% for error in form.last_name.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="form-group">
            {{ form.username.label(class="form-label") }}
            {{ form.username(class="form-control", placeholder="Enter a unique username") }}
            {% if form.username.errors %}
                <div class="form-error">
                    {% for error in form.username.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Username must be at least 4 characters long</small>
        </div>

        <div class="form-group">
            {{ form.email.label(class="form-label") }}
            {{ form.email(class="form-control", placeholder="Enter your email address") }}
            {% if form.email.errors %}
                <div class="form-error">
                    {% for error in form.email.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-row">
            <div class="form-group">
                {{ form.phone.label(class="form-label") }}
                {{ form.phone(class="form-control", placeholder="Enter your phone number") }}
                {% if form.phone.errors %}
                    <div class="form-error">
                        {% for error in form.phone.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
                <small class="form-help">Include area code (e.g., ************)</small>
            </div>

            <div class="form-group">
                {{ form.relationship_type.label(class="form-label") }}
                {{ form.relationship_type(class="form-control") }}
                {% if form.relationship_type.errors %}
                    <div class="form-error">
                        {% for error in form.relationship_type.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
                <small class="form-help">Your relationship to the children</small>
            </div>
        </div>

        <div class="form-group">
            {{ form.num_children.label(class="form-label") }}
            {{ form.num_children(class="form-control", id="num_children", onchange="updateChildrenFields()") }}
            {% if form.num_children.errors %}
                <div class="form-error">
                    {% for error in form.num_children.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select how many children you want to register</small>
        </div>

        <div id="children-section">
            <h4>Children Information</h4>

            <!-- Child 1 (always visible) -->
            <div class="child-form" id="child-1">
                <h5>Child 1</h5>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.child_1_name.label(class="form-label") }}
                        {{ form.child_1_name(class="form-control", placeholder="Enter child's full name") }}
                        {% if form.child_1_name.errors %}
                            <div class="form-error">
                                {% for error in form.child_1_name.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        {{ form.child_1_grade.label(class="form-label") }}
                        {{ form.child_1_grade(class="form-control") }}
                        {% if form.child_1_grade.errors %}
                            <div class="form-error">
                                {% for error in form.child_1_grade.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Child 2 -->
            <div class="child-form" id="child-2" style="display: none;">
                <h5>Child 2</h5>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.child_2_name.label(class="form-label") }}
                        {{ form.child_2_name(class="form-control", placeholder="Enter child's full name") }}
                    </div>
                    <div class="form-group">
                        {{ form.child_2_grade.label(class="form-label") }}
                        {{ form.child_2_grade(class="form-control") }}
                    </div>
                </div>
            </div>

            <!-- Child 3 -->
            <div class="child-form" id="child-3" style="display: none;">
                <h5>Child 3</h5>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.child_3_name.label(class="form-label") }}
                        {{ form.child_3_name(class="form-control", placeholder="Enter child's full name") }}
                    </div>
                    <div class="form-group">
                        {{ form.child_3_grade.label(class="form-label") }}
                        {{ form.child_3_grade(class="form-control") }}
                    </div>
                </div>
            </div>

            <!-- Child 4 -->
            <div class="child-form" id="child-4" style="display: none;">
                <h5>Child 4</h5>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.child_4_name.label(class="form-label") }}
                        {{ form.child_4_name(class="form-control", placeholder="Enter child's full name") }}
                    </div>
                    <div class="form-group">
                        {{ form.child_4_grade.label(class="form-label") }}
                        {{ form.child_4_grade(class="form-control") }}
                    </div>
                </div>
            </div>

            <!-- Child 5 -->
            <div class="child-form" id="child-5" style="display: none;">
                <h5>Child 5</h5>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.child_5_name.label(class="form-label") }}
                        {{ form.child_5_name(class="form-control", placeholder="Enter child's full name") }}
                    </div>
                    <div class="form-group">
                        {{ form.child_5_grade.label(class="form-label") }}
                        {{ form.child_5_grade(class="form-control") }}
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            {{ form.password.label(class="form-label") }}
            <div class="password-wrapper">
                {{ form.password(class="form-control", id="password", placeholder="Enter a secure password") }}
                <button type="button" class="show-hide" onclick="togglePassword()">👁️</button>
            </div>
            {% if form.password.errors %}
                <div class="form-error">
                    {% for error in form.password.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Password must be at least 8 characters with uppercase, lowercase, number, and special character</small>
            <div id="password-strength" class="password-strength"></div>
        </div>

        <div class="form-group">
            {{ form.confirm.label(class="form-label") }}
            {{ form.confirm(class="form-control", placeholder="Confirm your password") }}
            {% if form.confirm.errors %}
                <div class="form-error">
                    {% for error in form.confirm.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.submit(class="btn btn-warning animated-btn") }}
        </div>

        <div class="form-footer">
            <p><a href="{{ url_for('auth.register') }}">← Back to role selection</a></p>
            <p><a href="{{ url_for('auth.login') }}">Already have an account? Login here</a></p>
        </div>
    </form>
</div>

<style>
.role-header {
    text-align: center;
    margin-bottom: 2em;
}

.parent-header {
    border-bottom: 3px solid #ffc107;
    padding-bottom: 1em;
}

.role-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.form-description {
    color: var(--secondary);
    margin-bottom: 0;
    font-size: 0.95em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1em;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.85em;
    margin-top: 0.25em;
    display: block;
}

.form-footer {
    text-align: center;
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.form-footer a {
    color: var(--primary);
    text-decoration: none;
    display: block;
    margin: 0.5em 0;
}

.form-footer a:hover {
    text-decoration: underline;
}

.password-wrapper {
    position: relative;
}

.show-hide {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
}

@media (max-width: 600px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

#children-section {
    margin-top: 2em;
    padding: 1.5em;
    border: 2px solid var(--border);
    border-radius: 8px;
    background: rgba(255, 193, 7, 0.05);
}

#children-section h4 {
    color: #ffc107;
    margin-bottom: 1em;
    text-align: center;
}

.child-form {
    margin-bottom: 1.5em;
    padding: 1em;
    border: 1px solid var(--border);
    border-radius: 6px;
    background: var(--card-bg);
}

.child-form h5 {
    color: var(--primary);
    margin-bottom: 1em;
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.5em;
}

.password-strength {
    margin-top: 0.5em;
}

.password-checks {
    font-size: 0.85em;
    margin-top: 0.5em;
}

.check-pass {
    color: #28a745;
    margin: 0.2em 0;
}

.check-fail {
    color: #dc3545;
    margin: 0.2em 0;
}
</style>

<script>
function togglePassword() {
    var x = document.getElementById("password");
    if (x.type === "password") { x.type = "text"; } else { x.type = "password"; }
}

// Update children fields based on number selected
function updateChildrenFields() {
    const numChildren = parseInt(document.getElementById('num_children').value);

    // Hide all child forms first
    for (let i = 1; i <= 5; i++) {
        const childForm = document.getElementById(`child-${i}`);
        if (childForm) {
            if (i <= numChildren) {
                childForm.style.display = 'block';
                // Make required fields required
                const nameField = document.getElementById(`child_${i}_name`);
                const gradeField = document.getElementById(`child_${i}_grade`);
                if (nameField) nameField.required = true;
                if (gradeField) gradeField.required = true;
            } else {
                childForm.style.display = 'none';
                // Remove required attribute and clear values
                const nameField = document.getElementById(`child_${i}_name`);
                const gradeField = document.getElementById(`child_${i}_grade`);
                if (nameField) {
                    nameField.required = false;
                    nameField.value = '';
                }
                if (gradeField) {
                    gradeField.required = false;
                    gradeField.value = '';
                }
            }
        }
    }
}

// Real-time password validation
function validatePassword() {
    const password = document.getElementById('password').value;
    const strengthDiv = document.getElementById('password-strength');

    if (!password) {
        strengthDiv.innerHTML = '';
        return;
    }

    const checks = [
        { test: password.length >= 8, text: 'At least 8 characters' },
        { test: /[A-Z]/.test(password), text: 'Uppercase letter (A-Z)' },
        { test: /[a-z]/.test(password), text: 'Lowercase letter (a-z)' },
        { test: /[0-9]/.test(password), text: 'Number (0-9)' },
        { test: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password), text: 'Special character' }
    ];

    let html = '<div class="password-checks">';
    checks.forEach(check => {
        const icon = check.test ? '✓' : '✗';
        const className = check.test ? 'check-pass' : 'check-fail';
        html += `<div class="${className}">${icon} ${check.text}</div>`;
    });
    html += '</div>';

    strengthDiv.innerHTML = html;
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set up password validation
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', validatePassword);
    }

    // Initialize children fields
    updateChildrenFields();
});
</script>
{% endblock %}
