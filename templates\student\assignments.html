{% extends "base.html" %}

{% block title %}My Assignments{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>My Assignments</h1>
        <div class="action-buttons">
            <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if assignments %}
        <div class="assignments-grid">
            {% for assignment_data in assignments %}
                {% set assignment = assignment_data.assignment %}
                {% set class = assignment_data.class %}
                {% set submission = assignment_data.submission %}

                <div class="card assignment-card {% if submission %}submitted{% else %}pending{% endif %}">
                    <div class="card-header">
                        <h3>{{ assignment.title }}</h3>
                        <div class="assignment-status">
                            {% if submission %}
                                {% if submission.grade %}
                                    <span class="badge badge-success">Graded: {{ submission.grade }}</span>
                                {% else %}
                                    <span class="badge badge-info">Submitted</span>
                                {% endif %}
                            {% else %}
                                {% if assignment.due_date < now() %}
                                    <span class="badge badge-danger">Overdue</span>
                                {% else %}
                                    <span class="badge badge-warning">Pending</span>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="assignment-info">
                            <div class="info-item">
                                <strong>Class:</strong> {{ class.subject }}
                            </div>
                            <div class="info-item">
                                <strong>Teacher:</strong> {{ class.teacher.first_name }} {{ class.teacher.last_name }}
                            </div>
                            <div class="info-item">
                                <strong>Due Date:</strong>
                                <span class="{% if assignment.due_date < now() and not submission %}overdue{% endif %}">
                                    {{ assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}
                                </span>
                            </div>
                            {% if submission %}
                                <div class="info-item">
                                    <strong>Submitted:</strong> {{ submission.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}
                                </div>
                                {% if submission.feedback %}
                                    <div class="info-item">
                                        <strong>Feedback:</strong> {{ submission.feedback }}
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>

                        <div class="assignment-description">
                            <strong>Description:</strong>
                            <p>{{ assignment.description | nl2br | safe }}</p>
                        </div>
                    </div>

                    <div class="card-actions">
                        {% if submission %}
                            <div class="submission-info">
                                <span class="text-success">✓ Submitted</span>
                                {% if submission.file_path %}
                                    <a href="{{ url_for('download_file', filename=submission.file_path) }}" class="btn btn-outline-secondary btn-sm" title="Download your submission">
                                        📄 Download
                                    </a>
                                {% endif %}
                                {% if submission.grade %}
                                    <span class="badge badge-success">Grade: {{ submission.grade }}%</span>
                                {% else %}
                                    <span class="badge badge-info">Pending Grade</span>
                                {% endif %}
                            </div>
                        {% elif assignment.due_date > now() %}
                            <a href="{{ url_for('student.submit_assignment', assignment_id=assignment.id) }}" class="btn btn-primary btn-sm">Submit Assignment</a>
                        {% else %}
                            <div class="overdue-info">
                                <span class="text-danger">⚠️ Overdue</span>
                                <a href="{{ url_for('student.submit_assignment', assignment_id=assignment.id) }}" class="btn btn-warning btn-sm">Submit Late</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">📝</div>
            <h3>No Assignments</h3>
            <p>You don't have any assignments yet. Check back later or contact your teachers.</p>
        </div>
    {% endif %}
</div>

<style>
.assignments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5em;
    margin-top: 2em;
}

.assignment-card {
    border-left: 4px solid var(--warning);
}

.assignment-card.submitted {
    border-left-color: var(--success);
}

.assignment-card.pending {
    border-left-color: var(--warning);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1em;
}

.card-header h3 {
    margin: 0;
    color: var(--primary);
    flex: 1;
    margin-right: 1em;
}

.assignment-status {
    flex-shrink: 0;
}

.assignment-info {
    margin: 1em 0;
}

.info-item {
    margin-bottom: 0.5em;
    padding: 0.25em 0;
}

.assignment-description {
    margin: 1em 0;
    padding: 1em;
    background: var(--background);
    border-radius: 5px;
    border: 1px solid var(--border);
}

.assignment-description p {
    margin: 0.5em 0 0 0;
}

.card-actions {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
    text-align: center;
}

.submission-info {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    align-items: center;
}

.submission-info .btn {
    margin: 0.25em;
}

.overdue-info {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    align-items: center;
}

.overdue-info .text-danger {
    font-weight: bold;
}

.overdue {
    color: var(--danger);
    font-weight: bold;
}

.text-success {
    color: var(--success);
    font-weight: bold;
}

.text-danger {
    color: var(--danger);
    font-weight: bold;
}

.badge-info {
    background-color: var(--info);
}

.badge-warning {
    background-color: var(--warning);
    color: #000;
}

.badge-danger {
    background-color: var(--danger);
}

.badge-success {
    background-color: var(--success);
}

.empty-state {
    text-align: center;
    padding: 3em 1em;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    flex-wrap: wrap;
    gap: 1em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
    }

    .assignments-grid {
        grid-template-columns: 1fr;
    }

    .card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5em;
    }

    .card-header h3 {
        margin-right: 0;
    }
}
</style>
{% endblock %}
