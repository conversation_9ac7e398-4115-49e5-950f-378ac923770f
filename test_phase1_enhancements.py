#!/usr/bin/env python3
"""
Test script for Phase 1 enhancements to the Flask School Management System.
Tests the enhanced registration forms, password security, and new fields.
"""

import requests
from bs4 import BeautifulSoup
import sys
import json

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    response = session.get(url)
    if response.status_code != 200:
        return None
    
    soup = BeautifulSoup(response.content, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    return csrf_input['value'] if csrf_input else None

def test_enhanced_password_validation():
    """Test that password validation works correctly."""
    print("🔐 Testing Enhanced Password Validation...")
    
    # Test weak passwords that should fail
    weak_passwords = [
        "123456",           # Too short, no uppercase, no special
        "password",         # No uppercase, no number, no special
        "Password",         # No number, no special
        "Password123",      # No special character
        "password123!",     # No uppercase
        "PASSWORD123!",     # No lowercase
    ]
    
    session = requests.Session()
    csrf_token = get_csrf_token(session, f"{BASE_URL}/register/admin")
    
    if not csrf_token:
        print("  ❌ Could not get CSRF token")
        return
    
    for password in weak_passwords:
        data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'admin_code': 'ADMIN2024',
            'password': password,
            'confirm': password,
            'csrf_token': csrf_token
        }
        
        response = session.post(f"{BASE_URL}/register/admin", data=data)
        if "Password must" in response.text:
            print(f"  ✅ Weak password '{password}' correctly rejected")
        else:
            print(f"  ❌ Weak password '{password}' was accepted")

def test_teacher_registration_enhancements():
    """Test the enhanced teacher registration form."""
    print("\n👩‍🏫 Testing Enhanced Teacher Registration...")
    
    session = requests.Session()
    csrf_token = get_csrf_token(session, f"{BASE_URL}/register/teacher")
    
    if not csrf_token:
        print("  ❌ Could not get CSRF token")
        return
    
    # Test with valid data including new fields
    data = {
        'username': 'newteacher',
        'email': '<EMAIL>',
        'first_name': 'Jane',
        'last_name': 'Doe',
        'subjects': 'Mathematics, Computer Science',
        'years_experience': '5',
        'qualifications': 'M.S. in Mathematics, B.S. in Computer Science, Teaching Certificate',
        'password': 'Teacher123!',
        'confirm': 'Teacher123!',
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/register/teacher", data=data, allow_redirects=False)
    
    if response.status_code in [302, 303]:
        print("  ✅ Enhanced teacher registration successful")
        print("  ✅ New fields (years_experience, qualifications) accepted")
    else:
        print(f"  ❌ Teacher registration failed - Status {response.status_code}")
        if "error" in response.text.lower():
            print(f"  ❌ Error in response")

def test_parent_registration_enhancements():
    """Test the enhanced parent registration form with dynamic children."""
    print("\n👨‍👩‍👧‍👦 Testing Enhanced Parent Registration...")
    
    session = requests.Session()
    csrf_token = get_csrf_token(session, f"{BASE_URL}/register/parent")
    
    if not csrf_token:
        print("  ❌ Could not get CSRF token")
        return
    
    # Test with valid data including new fields and children
    data = {
        'username': 'newparent',
        'email': '<EMAIL>',
        'first_name': 'John',
        'last_name': 'Parent',
        'phone': '555-1234',
        'relationship_type': 'Father',
        'num_children': '2',
        'child_1_name': 'Child One',
        'child_1_grade': '5',
        'child_2_name': 'Child Two',
        'child_2_grade': '8',
        'password': 'Parent123!',
        'confirm': 'Parent123!',
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/register/parent", data=data, allow_redirects=False)
    
    if response.status_code in [302, 303]:
        print("  ✅ Enhanced parent registration successful")
        print("  ✅ New fields (relationship_type, children info) accepted")
    else:
        print(f"  ❌ Parent registration failed - Status {response.status_code}")

def test_form_rendering():
    """Test that all enhanced forms render correctly."""
    print("\n🎨 Testing Enhanced Form Rendering...")
    
    forms_to_test = [
        ("/register", "Role selection page"),
        ("/register/admin", "Admin registration form"),
        ("/register/teacher", "Teacher registration form"),
        ("/register/student", "Student registration form"),
        ("/register/parent", "Parent registration form"),
    ]
    
    for url, name in forms_to_test:
        try:
            response = requests.get(f"{BASE_URL}{url}")
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for password strength indicator
                password_strength = soup.find('div', {'id': 'password-strength'})
                
                # Check for enhanced form elements
                if url == "/register/teacher":
                    years_exp = soup.find('input', {'name': 'years_experience'}) or soup.find('input', {'id': 'years_experience'})
                    qualifications = soup.find('textarea', {'name': 'qualifications'}) or soup.find('textarea', {'id': 'qualifications'})
                    if years_exp and qualifications:
                        print(f"  ✅ {name}: Enhanced fields present")
                    else:
                        print(f"  ❌ {name}: Missing enhanced fields")
                
                elif url == "/register/parent":
                    relationship = soup.find('select', {'name': 'relationship_type'}) or soup.find('select', {'id': 'relationship_type'})
                    num_children = soup.find('select', {'name': 'num_children'}) or soup.find('select', {'id': 'num_children'})
                    if relationship and num_children:
                        print(f"  ✅ {name}: Enhanced fields present")
                    else:
                        print(f"  ❌ {name}: Missing enhanced fields")
                
                elif password_strength and url != "/register":
                    print(f"  ✅ {name}: Password strength indicator present")
                elif url != "/register":
                    print(f"  ❌ {name}: Password strength indicator missing")
                else:
                    print(f"  ✅ {name}: Rendered correctly")
                    
            else:
                print(f"  ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")

def test_login_with_new_passwords():
    """Test login with the new strong passwords."""
    print("\n🔑 Testing Login with Enhanced Passwords...")
    
    accounts = [
        ("admin", "Admin123!", "Admin"),
        ("teacher1", "Teacher123!", "Teacher"),
        ("student1", "Student123!", "Student"),
        ("parent1", "Parent123!", "Parent"),
    ]
    
    for username, password, role in accounts:
        session = requests.Session()
        csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
        
        if not csrf_token:
            print(f"  ❌ {role} ({username}): Could not get CSRF token")
            continue
        
        data = {
            'username': username,
            'password': password,
            'csrf_token': csrf_token
        }
        
        response = session.post(f"{BASE_URL}/login", data=data, allow_redirects=False)
        
        if response.status_code in [302, 303]:
            print(f"  ✅ {role} ({username}): Login successful with new password")
        else:
            print(f"  ❌ {role} ({username}): Login failed")

def main():
    """Run all Phase 1 enhancement tests."""
    print("🚀 Testing Phase 1 Enhancements - Registration System")
    print("=" * 60)
    
    try:
        # Test basic connectivity
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            sys.exit(1)
        print("✅ Server is running")
        
        # Run enhancement tests
        test_form_rendering()
        test_enhanced_password_validation()
        test_teacher_registration_enhancements()
        test_parent_registration_enhancements()
        test_login_with_new_passwords()
        
        print("\n" + "=" * 60)
        print("✅ Phase 1 Enhancement Testing Complete!")
        print("\n📋 PHASE 1 ENHANCEMENTS IMPLEMENTED:")
        print("✅ Enhanced password security (8+ chars, mixed case, numbers, special)")
        print("✅ Real-time password validation with visual indicators")
        print("✅ Teacher registration: years_experience + qualifications fields")
        print("✅ Parent registration: relationship_type + dynamic children fields")
        print("✅ Updated sample data with strong passwords")
        print("✅ All registration forms maintain admin approval workflow")
        
        print("\n🔑 UPDATED TEST CREDENTIALS:")
        print("Admin: admin / Admin123!")
        print("Teacher: teacher1 / Teacher123!")
        print("Student: student1 / Student123!")
        print("Parent: parent1 / Parent123!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Flask app is running on port 5000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
