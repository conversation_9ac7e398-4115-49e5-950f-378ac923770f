#!/usr/bin/env python3
"""
Test to check what routes are actually registered.
"""

def test_app_routes():
    """Test what routes are registered in the app."""
    print("🔍 Testing app routes...")
    
    try:
        from app import create_app
        app = create_app()
        
        print(f"✅ App created successfully")
        print(f"App name: {app.name}")
        
        # Get all routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'rule': str(rule),
                'methods': list(rule.methods),
                'endpoint': rule.endpoint
            })
        
        print(f"\n📋 Found {len(routes)} routes:")
        
        # Sort routes for better readability
        routes.sort(key=lambda x: x['rule'])
        
        for route in routes:
            methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
            print(f"  {route['rule']} -> {route['endpoint']} ({', '.join(methods)})")
        
        # Check for specific routes we need
        print(f"\n🔍 Checking for key routes:")
        key_routes = ['/login', '/register', '/admin/dashboard', '/teacher/dashboard']
        
        for key_route in key_routes:
            found = any(key_route in route['rule'] for route in routes)
            print(f"  {'✅' if found else '❌'} {key_route}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Route Registration Test")
    print("=" * 40)
    
    success = test_app_routes()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Route test completed")
    else:
        print("❌ Route test failed")
