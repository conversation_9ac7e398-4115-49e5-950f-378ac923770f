from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from functools import wraps
from models import db, User, Teacher, Class, Assignment, Submission, Student, ClassEnrollment
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from flask_wtf import <PERSON>laskForm
from wtforms import StringField, TextAreaField, DateTimeField, SubmitField, SelectField, IntegerField
from wtforms.validators import DataRequired, Length, NumberRange
from flask_wtf.file import FileField, FileAllowed

# Create the teacher blueprint with explicit name
teacher = Blueprint('teacher', __name__)

# Teacher access decorator
def teacher_required(f):
    """Decorator to require teacher role for a route."""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'teacher':
            flash('Access denied. Teacher privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Teacher profile form
class TeacherProfileForm(FlaskForm):
    """Form for creating/editing teacher profile."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    subjects = StringField('Subjects (comma separated)', validators=[DataRequired(), Length(max=255)])
    submit = SubmitField('Save Profile')

# Class form
class ClassForm(FlaskForm):
    """Form for creating/editing a class."""
    subject = StringField('Subject', validators=[DataRequired(), Length(max=64)])
    schedule_time = DateTimeField('Schedule Time', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    room = StringField('Room', validators=[DataRequired(), Length(max=20)])
    submit = SubmitField('Save Class')

# Assignment form
class AssignmentForm(FlaskForm):
    """Form for creating/editing an assignment."""
    title = StringField('Title', validators=[DataRequired(), Length(max=128)])
    description = TextAreaField('Description', validators=[DataRequired()])
    due_date = DateTimeField('Due Date', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    class_id = SelectField('Class', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Save Assignment')

# Enhanced grading form
class GradingForm(FlaskForm):
    """Form for grading student submissions with detailed feedback."""
    grade = IntegerField('Grade (0-100)', validators=[DataRequired(), NumberRange(min=0, max=100)])
    feedback = TextAreaField('Detailed Feedback', validators=[DataRequired()],
                            render_kw={"placeholder": "Provide constructive feedback on the student's work..."})
    submit = SubmitField('Save Grade & Feedback')

# Grading form
class GradingForm(FlaskForm):
    """Form for grading student submissions."""
    grade = IntegerField('Grade (0-100)', validators=[DataRequired(), NumberRange(min=0, max=100)])
    feedback = TextAreaField('Feedback', validators=[DataRequired()],
                            render_kw={"placeholder": "Provide detailed feedback on the student's work..."})
    submit = SubmitField('Save Grade')

# Teacher dashboard route
@teacher.route('/dashboard')
@teacher_required
def dashboard():
    """Teacher dashboard page."""
    # Get the teacher profile
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    # Get classes taught by this teacher
    classes = Class.query.filter_by(teacher_id=teacher_profile.id).all()
    classes_count = len(classes)

    # Get student count
    students_count = 0
    for class_obj in classes:
        students_count += class_obj.enrollments.count()

    # Get assignments count
    assignments = Assignment.query.join(Class).filter(
        Class.teacher_id == teacher_profile.id
    ).all()
    assignments_count = len(assignments)

    # Get submissions count
    submissions_count = 0
    for assignment in assignments:
        submissions_count += assignment.submissions.count()

    # Get pending submissions to grade
    pending_submissions = Submission.query.join(Assignment).join(Class).filter(
        Class.teacher_id == teacher_profile.id,
        Submission.grade == None
    ).count()

    return render_template('dashboards/teacher.html',
                          teacher=teacher_profile,
                          classes=classes,
                          classes_count=classes_count,
                          students_count=students_count,
                          assignments_count=assignments_count,
                          submissions_count=submissions_count,
                          pending_submissions=pending_submissions)

# Teacher profile route
@teacher.route('/profile', methods=['GET', 'POST'])
@teacher_required
def profile():
    """Create or edit teacher profile."""
    # Check if profile already exists
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()
    form = TeacherProfileForm(obj=teacher_profile)

    if form.validate_on_submit():
        if teacher_profile:
            # Update existing profile
            teacher_profile.first_name = form.first_name.data
            teacher_profile.last_name = form.last_name.data
            teacher_profile.subjects = form.subjects.data
            flash('Teacher profile updated successfully!', 'success')
        else:
            # Create new teacher profile
            teacher_profile = Teacher(
                user_id=current_user.id,
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                subjects=form.subjects.data
            )
            db.session.add(teacher_profile)
            flash('Teacher profile created successfully!', 'success')

        db.session.commit()
        return redirect(url_for('teacher.dashboard'))

    return render_template('teacher/profile.html', form=form, teacher=teacher_profile)

# Classes list route
@teacher.route('/classes')
@teacher_required
def classes():
    """List of classes taught by the teacher."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    classes = Class.query.filter_by(teacher_id=teacher_profile.id).order_by(Class.schedule_time).all()

    return render_template('teacher/classes.html', classes=classes)

# Create class route
@teacher.route('/classes/create', methods=['GET', 'POST'])
@teacher_required
def create_class():
    """Create a new class."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    form = ClassForm()

    if form.validate_on_submit():
        # Create new class
        new_class = Class(
            teacher_id=teacher_profile.id,
            subject=form.subject.data,
            schedule_time=form.schedule_time.data,
            room=form.room.data
        )

        db.session.add(new_class)
        db.session.commit()

        flash('Class created successfully!', 'success')
        return redirect(url_for('teacher.classes'))

    return render_template('teacher/class_form.html', form=form, title='Create Class')

# Class detail route
@teacher.route('/class/<int:class_id>')
@teacher_required
def class_detail(class_id):
    """Teacher class detail page."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    class_obj = Class.query.get_or_404(class_id)

    # Ensure the teacher owns this class
    if class_obj.teacher_id != teacher_profile.id:
        flash('You do not have permission to view this class.', 'danger')
        return redirect(url_for('teacher.classes'))

    return render_template('teacher/class_detail.html', class_obj=class_obj)

# Create assignment route
@teacher.route('/assignment/create', methods=['GET', 'POST'])
@teacher_required
def create_assignment():
    """Create a new assignment."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    form = AssignmentForm()

    # Populate class choices with teacher's classes
    classes = Class.query.filter_by(teacher_id=teacher_profile.id).all()
    form.class_id.choices = [(c.id, f"{c.subject} - {c.room}") for c in classes]

    if form.validate_on_submit():
        # Create new assignment
        assignment = Assignment(
            class_id=form.class_id.data,
            title=form.title.data,
            description=form.description.data,
            due_date=form.due_date.data
        )

        db.session.add(assignment)
        db.session.commit()

        flash('Assignment created successfully!', 'success')
        return redirect(url_for('teacher.assignments'))

    return render_template('teacher/assignment_form.html', form=form, title='Create Assignment')

# Assignments list route
@teacher.route('/assignments')
@teacher_required
def assignments():
    """Enhanced list of assignments created by the teacher with submission status."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    assignments = Assignment.query.join(Class).filter(
        Class.teacher_id == teacher_profile.id
    ).order_by(Assignment.due_date.desc()).all()

    # Add submission statistics for each assignment
    assignment_stats = []
    for assignment in assignments:
        total_students = ClassEnrollment.query.filter_by(class_id=assignment.class_id).count()
        submitted_count = assignment.submissions.count()
        graded_count = assignment.submissions.filter(Submission.grade.isnot(None)).count()

        assignment_stats.append({
            'assignment': assignment,
            'total_students': total_students,
            'submitted_count': submitted_count,
            'graded_count': graded_count,
            'pending_grade': submitted_count - graded_count
        })

    return render_template('teacher/assignments.html', assignment_stats=assignment_stats)

# Assignment detail route
@teacher.route('/assignment/<int:assignment_id>')
@teacher_required
def assignment_detail(assignment_id):
    """Enhanced teacher assignment detail page with submissions and grading."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    assignment = Assignment.query.get_or_404(assignment_id)

    # Ensure the teacher owns this assignment
    if assignment.class_.teacher_id != teacher_profile.id:
        flash('You do not have permission to view this assignment.', 'danger')
        return redirect(url_for('teacher.assignments'))

    # Get all students enrolled in this class
    enrolled_students = db.session.query(Student).join(ClassEnrollment).filter(
        ClassEnrollment.class_id == assignment.class_id
    ).all()

    # Get submissions for this assignment
    submissions = assignment.submissions.all()
    submission_dict = {sub.student_id: sub for sub in submissions}

    # Create comprehensive student list with submission status
    student_submissions = []
    for student in enrolled_students:
        submission = submission_dict.get(student.id)
        student_submissions.append({
            'student': student,
            'submission': submission,
            'status': 'graded' if submission and submission.grade is not None else
                     'submitted' if submission else 'not_submitted'
        })

    # Sort by submission status and student name
    student_submissions.sort(key=lambda x: (
        0 if x['status'] == 'submitted' else 1 if x['status'] == 'graded' else 2,
        x['student'].first_name
    ))

    return render_template('teacher/assignment_detail.html',
                         assignment=assignment,
                         student_submissions=student_submissions)

# Grade submission route
@teacher.route('/submission/<int:submission_id>/grade', methods=['GET', 'POST'])
@teacher_required
def grade_submission(submission_id):
    """Enhanced grading interface for student submissions."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    submission = Submission.query.get_or_404(submission_id)

    # Ensure the teacher owns this assignment
    if submission.assignment.class_.teacher_id != teacher_profile.id:
        flash('You do not have permission to grade this submission.', 'danger')
        return redirect(url_for('teacher.assignments'))

    form = GradingForm()

    # Pre-populate form with existing grade/feedback if available
    if request.method == 'GET' and submission.grade is not None:
        form.grade.data = submission.grade
        form.feedback.data = submission.feedback

    if form.validate_on_submit():
        submission.grade = form.grade.data
        submission.feedback = form.feedback.data
        submission.graded_at = datetime.now()
        db.session.commit()

        flash(f'Grade saved for {submission.student.first_name} {submission.student.last_name}!', 'success')
        return redirect(url_for('teacher.assignment_detail', assignment_id=submission.assignment_id))

    return render_template('teacher/grade_submission.html',
                         submission=submission,
                         form=form)

# Enhanced class management route
@teacher.route('/classes/manage')
@teacher_required
def manage_classes():
    """Enhanced class management with student performance overview."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    classes = Class.query.filter_by(teacher_id=teacher_profile.id).all()

    class_data = []
    for class_obj in classes:
        # Get enrolled students
        enrollments = ClassEnrollment.query.filter_by(class_id=class_obj.id).all()
        students = [enrollment.student for enrollment in enrollments]

        # Get assignments for this class
        assignments = Assignment.query.filter_by(class_id=class_obj.id).all()

        # Calculate class performance statistics
        total_submissions = 0
        graded_submissions = 0
        total_grade_points = 0

        for assignment in assignments:
            submissions = assignment.submissions.all()
            total_submissions += len(submissions)

            for submission in submissions:
                if submission.grade is not None:
                    graded_submissions += 1
                    total_grade_points += submission.grade

        average_grade = (total_grade_points / graded_submissions) if graded_submissions > 0 else 0

        class_data.append({
            'class': class_obj,
            'students': students,
            'student_count': len(students),
            'assignment_count': len(assignments),
            'average_grade': round(average_grade, 1),
            'completion_rate': round((graded_submissions / total_submissions * 100) if total_submissions > 0 else 0, 1)
        })

    return render_template('teacher/manage_classes.html', class_data=class_data)


