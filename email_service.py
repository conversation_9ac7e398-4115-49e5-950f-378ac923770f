"""
Email Service Module for Flask School Management System
------------------------------------------------------
Handles OTP email sending and other email notifications.
"""

from flask import current_app, render_template
from flask_mail import Message, Mail
from models import OTP, User
from db import db
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mail_instance():
    """Get Flask-Mail instance from current app context."""
    try:
        return current_app.extensions.get('mail')
    except Exception as e:
        logger.error(f"Failed to get mail instance: {e}")
        return None

def send_otp_email(user_id, otp_code):
    """
    Send OTP verification email to user.
    
    Args:
        user_id (int): User ID to send OTP to
        otp_code (str): The OTP code to send
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Get user information
        user = User.query.get(user_id)
        if not user:
            logger.error(f"User with ID {user_id} not found")
            return False
            
        # Get mail instance
        mail = get_mail_instance()
        if not mail:
            logger.error("Mail instance not available")
            return False
            
        # Create email message
        subject = "Your Login Verification Code - School Management System"
        
        # Create HTML email body
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Login Verification Code</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #007bff;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 5px 5px 0 0;
                }}
                .content {{
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 5px 5px;
                }}
                .otp-code {{
                    background-color: #007bff;
                    color: white;
                    font-size: 32px;
                    font-weight: bold;
                    text-align: center;
                    padding: 20px;
                    margin: 20px 0;
                    border-radius: 5px;
                    letter-spacing: 5px;
                }}
                .warning {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    color: #666;
                    font-size: 12px;
                    margin-top: 30px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔐 Login Verification</h1>
                <p>School Management System</p>
            </div>
            
            <div class="content">
                <h2>Hello {user.username}!</h2>
                
                <p>You are attempting to log in to the School Management System. To complete your login, please use the verification code below:</p>
                
                <div class="otp-code">{otp_code}</div>
                
                <div class="warning">
                    <strong>⚠️ Important Security Information:</strong>
                    <ul>
                        <li>This code will expire in 5 minutes</li>
                        <li>Never share this code with anyone</li>
                        <li>If you didn't request this code, please ignore this email</li>
                        <li>Contact your system administrator if you have concerns</li>
                    </ul>
                </div>
                
                <p>If you're having trouble logging in, please contact your system administrator.</p>
                
                <p>Best regards,<br>
                School Management System Team</p>
            </div>
            
            <div class="footer">
                <p>This is an automated message. Please do not reply to this email.</p>
                <p>© 2024 School Management System. All rights reserved.</p>
            </div>
        </body>
        </html>
        """
        
        # Create plain text version
        text_body = f"""
        Login Verification Code - School Management System
        
        Hello {user.username}!
        
        You are attempting to log in to the School Management System.
        To complete your login, please use the verification code below:
        
        Verification Code: {otp_code}
        
        IMPORTANT SECURITY INFORMATION:
        - This code will expire in 5 minutes
        - Never share this code with anyone
        - If you didn't request this code, please ignore this email
        - Contact your system administrator if you have concerns
        
        If you're having trouble logging in, please contact your system administrator.
        
        Best regards,
        School Management System Team
        
        ---
        This is an automated message. Please do not reply to this email.
        © 2024 School Management System. All rights reserved.
        """
        
        # Create message
        msg = Message(
            subject=subject,
            recipients=[user.email],
            html=html_body,
            body=text_body
        )
        
        # Send email
        mail.send(msg)
        logger.info(f"OTP email sent successfully to {user.email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send OTP email to user {user_id}: {e}")
        return False

def create_and_send_otp(user_id):
    """
    Create a new OTP and send it via email.
    
    Args:
        user_id (int): User ID to create and send OTP for
        
    Returns:
        tuple: (success: bool, otp_instance: OTP or None, message: str)
    """
    try:
        # Clean up any existing valid OTPs for this user
        existing_otps = OTP.query.filter_by(user_id=user_id, is_used=False).all()
        for otp in existing_otps:
            otp.is_used = True
        
        # Create new OTP
        otp = OTP(user_id=user_id, expiry_minutes=current_app.config.get('OTP_EXPIRY_MINUTES', 5))
        db.session.add(otp)
        db.session.commit()
        
        # Send OTP email
        if send_otp_email(user_id, otp.otp_code):
            return True, otp, "OTP sent successfully"
        else:
            # If email sending fails, mark OTP as used to prevent misuse
            otp.is_used = True
            db.session.commit()
            return False, None, "Failed to send OTP email"
            
    except Exception as e:
        logger.error(f"Failed to create and send OTP for user {user_id}: {e}")
        db.session.rollback()
        return False, None, f"Error creating OTP: {str(e)}"

def test_email_configuration():
    """
    Test email configuration by sending a test email.
    
    Returns:
        bool: True if test email sent successfully, False otherwise
    """
    try:
        mail = get_mail_instance()
        if not mail:
            return False
            
        # Send test email to configured sender
        test_recipient = current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
        
        msg = Message(
            subject="Email Configuration Test - School Management System",
            recipients=[test_recipient],
            body="This is a test email to verify email configuration is working correctly."
        )
        
        mail.send(msg)
        logger.info("Test email sent successfully")
        return True
        
    except Exception as e:
        logger.error(f"Email configuration test failed: {e}")
        return False
