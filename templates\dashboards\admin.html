{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>Admin Dashboard</h1>
    <p class="dashboard-welcome">Welcome, {{ current_user.username }}!</p>

    <div class="dashboard-stats">
        <div class="card">
            <h3>Students</h3>
            <p class="stat">{{ students_count }}</p>
        </div>
        <div class="card">
            <h3>Teachers</h3>
            <p class="stat">{{ teachers_count }}</p>
        </div>
        <div class="card">
            <h3>Classes</h3>
            <p class="stat">{{ classes_count }}</p>
        </div>
        <div class="card">
            <h3>Parents</h3>
            <p class="stat">{{ parents_count }}</p>
        </div>
        <div class="card">
            <h3>Pending Approvals</h3>
            <p class="stat">{{ pending_users }}</p>
        </div>
    </div>

    <div class="dashboard-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('admin.overview') }}" class="btn btn-primary">📊 System Overview</a>
            <a href="{{ url_for('admin.notifications') }}" class="btn btn-info">📢 Manage Notifications</a>
            <a href="{{ url_for('admin.users') }}" class="btn">👥 Manage Users</a>
            <a href="{{ url_for('admin.pending_approvals') }}" class="btn">⏳ Pending Approvals</a>
            <a href="{{ url_for('admin.database') }}" class="btn">🗄️ Database Viewer</a>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>System Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <strong>Current Time:</strong> {{ now().strftime('%Y-%m-%d %H:%M:%S') }}
            </div>
            <div class="info-item">
                <strong>Server Status:</strong> <span class="status-ok">Running</span>
            </div>
            <div class="info-item">
                <strong>Database:</strong> <span class="status-ok">Connected</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}