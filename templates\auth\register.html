{% extends "base.html" %}

{% block title %}Register - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <div class="role-selector">
        <h2>Create Account</h2>
        <p class="form-description">Choose your role to get started with the registration process.</p>
        
        <div class="role-cards">
            <a href="{{ url_for('auth.register_admin') }}" class="role-card admin-card">
                <div class="role-icon">👨‍💼</div>
                <h3>Administrator</h3>
                <p>Manage the school system, users, and settings</p>
                <small>Requires admin code</small>
            </a>
            
            <a href="{{ url_for('auth.register_teacher') }}" class="role-card teacher-card">
                <div class="role-icon">👩‍🏫</div>
                <h3>Teacher</h3>
                <p>Create classes, assignments, and manage students</p>
                <small>Requires admin approval</small>
            </a>
            
            <a href="{{ url_for('auth.register_student') }}" class="role-card student-card">
                <div class="role-icon">👨‍🎓</div>
                <h3>Student</h3>
                <p>Access classes, submit assignments, and track progress</p>
                <small>Requires admin approval</small>
            </a>
            
            <a href="{{ url_for('auth.register_parent') }}" class="role-card parent-card">
                <div class="role-icon">👨‍👩‍👧‍👦</div>
                <h3>Parent</h3>
                <p>Monitor your child's academic progress and activities</p>
                <small>Requires admin approval</small>
            </a>
        </div>
        
        <div class="form-footer">
            <p><a href="{{ url_for('auth.login') }}">Already have an account? Login here</a></p>
        </div>
    </div>
</div>

<style>
.role-selector {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.form-description {
    color: var(--secondary);
    margin-bottom: 2em;
    font-size: 1.1em;
}

.role-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5em;
    margin: 2em 0;
}

.role-card {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: 12px;
    padding: 1.5em;
    text-decoration: none;
    color: var(--text);
    transition: all 0.3s ease;
    display: block;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    text-decoration: none;
    color: var(--text);
}

.admin-card:hover { border-color: #dc3545; }
.teacher-card:hover { border-color: #28a745; }
.student-card:hover { border-color: #007bff; }
.parent-card:hover { border-color: #ffc107; }

.role-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.role-card h3 {
    margin: 0.5em 0;
    color: var(--primary);
}

.role-card p {
    margin: 0.5em 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.role-card small {
    color: var(--muted);
    font-style: italic;
}

.form-footer {
    margin-top: 2em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.form-footer a {
    color: var(--primary);
    text-decoration: none;
}

.form-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 600px) {
    .role-cards {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
