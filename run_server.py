#!/usr/bin/env python3
"""
Flask School Management System Server
====================================
A comprehensive school management system with Phase 3 enhancements.
"""

import sys

def main():
    print("🚀 Flask School Management System - Phase 3 Enhanced")
    print("=" * 60)

    try:
        # Import and create the app
        from app import create_app
        print("✅ App module imported successfully")

        app = create_app()
        print("✅ Flask app created successfully")

        # Display server information
        print("\n🌐 Server Information:")
        print(f"  📍 URL: http://127.0.0.1:5000")
        print(f"  🔧 Debug Mode: Enabled")
        print(f"  📊 Routes Registered: {len(list(app.url_map.iter_rules()))}")

        print("\n📋 Phase 3 Enhanced Features:")
        print("  ✅ Enhanced Teacher Assignment Management")
        print("  ✅ Advanced Grading System with Feedback")
        print("  ✅ Class Management Dashboard")
        print("  ✅ Admin Notification System")
        print("  ✅ System Overview with Analytics")
        print("  ✅ Enhanced Database Schema")

        print("\n🔑 Test Credentials:")
        print("  👑 Admin: admin / Admin123!")
        print("  👨‍🏫 Teacher: teacher1 / Teacher123!")
        print("  👨‍🎓 Student: student1 / Student123!")
        print("  👨‍👩‍👧‍👦 Parent: parent1 / Parent123!")

        print("\n🎯 Phase 3 Test URLs:")
        print("  📊 Admin System Overview: http://127.0.0.1:5000/admin/overview")
        print("  📢 Admin Notifications: http://127.0.0.1:5000/admin/notifications")
        print("  🏫 Teacher Class Management: http://127.0.0.1:5000/teacher/classes/manage")
        print("  📝 Teacher Assignments: http://127.0.0.1:5000/teacher/assignments")

        print("\n" + "=" * 60)
        print("🚀 Starting server... Press Ctrl+C to stop")
        print("🌐 Open http://127.0.0.1:5000 in your browser")
        print("=" * 60)

        # Start the server
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)

    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
