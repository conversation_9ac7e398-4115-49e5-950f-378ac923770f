#!/usr/bin/env python3
"""
Simple server runner for the Flask School Management System.
"""

if __name__ == "__main__":
    print("🚀 Starting Flask School Management System...")
    
    try:
        from app import create_app
        print("✅ App module imported")
        
        app = create_app()
        print("✅ App created")
        
        print("🌐 Server starting at: http://127.0.0.1:5000")
        print("📋 Available routes:")
        print("  - Home: http://127.0.0.1:5000/")
        print("  - Login: http://127.0.0.1:5000/login")
        print("  - Register: http://127.0.0.1:5000/register")
        print("  - Admin: http://127.0.0.1:5000/admin/dashboard")
        print("  - Teacher: http://127.0.0.1:5000/teacher/dashboard")
        print("  - Student: http://127.0.0.1:5000/student/dashboard")
        print("\n🔑 Test Credentials:")
        print("  Admin: admin / Admin123!")
        print("  Teacher: teacher1 / Teacher123!")
        print("  Student: student1 / Student123!")
        print("  Parent: parent1 / Parent123!")
        print("\n" + "="*50)
        
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
