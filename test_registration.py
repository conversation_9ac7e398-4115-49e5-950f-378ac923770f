#!/usr/bin/env python3
"""
Test script to verify the registration system functionality.
"""

import requests
from bs4 import BeautifulSoup
import sys

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    response = session.get(url)
    if response.status_code != 200:
        return None
    
    soup = BeautifulSoup(response.content, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    return csrf_input['value'] if csrf_input else None

def test_page_access():
    """Test that all registration pages are accessible."""
    print("🔍 Testing page access...")
    
    pages = [
        ("/", "Home page"),
        ("/login", "Login page"),
        ("/register", "General registration page"),
        ("/register/admin", "Admin registration page"),
        ("/register/teacher", "Teacher registration page"),
        ("/register/student", "Student registration page"),
        ("/register/parent", "Parent registration page"),
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{url}")
            if response.status_code == 200:
                print(f"  ✅ {name}: OK")
            else:
                print(f"  ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")

def test_login_with_sample_accounts():
    """Test login with the sample accounts created."""
    print("\n🔍 Testing login with sample accounts...")
    
    accounts = [
        ("admin", "admin123", "Admin"),
        ("teacher1", "teacher123", "Teacher"),
        ("student1", "student123", "Student"),
        ("parent1", "parent123", "Parent"),
    ]
    
    for username, password, role in accounts:
        session = requests.Session()
        
        # Get CSRF token
        csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
        if not csrf_token:
            print(f"  ❌ {role} ({username}): Could not get CSRF token")
            continue
        
        # Attempt login
        login_data = {
            'username': username,
            'password': password,
            'csrf_token': csrf_token
        }
        
        response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        
        if response.status_code in [302, 303]:  # Redirect indicates successful login
            print(f"  ✅ {role} ({username}): Login successful")
            
            # Test dashboard access
            dashboard_response = session.get(response.headers.get('Location', f"{BASE_URL}/"))
            if dashboard_response.status_code == 200:
                print(f"    ✅ Dashboard access: OK")
            else:
                print(f"    ❌ Dashboard access: Status {dashboard_response.status_code}")
        else:
            print(f"  ❌ {role} ({username}): Login failed - Status {response.status_code}")

def test_registration_forms():
    """Test that registration forms are properly rendered."""
    print("\n🔍 Testing registration form rendering...")
    
    forms = [
        ("/register/admin", "Admin registration form"),
        ("/register/teacher", "Teacher registration form"),
        ("/register/student", "Student registration form"),
        ("/register/parent", "Parent registration form"),
    ]
    
    for url, name in forms:
        try:
            response = requests.get(f"{BASE_URL}{url}")
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for form elements
                form = soup.find('form')
                csrf_token = soup.find('input', {'name': 'csrf_token'})
                submit_button = soup.find('input', {'type': 'submit'}) or soup.find('button', {'type': 'submit'})
                
                if form and csrf_token and submit_button:
                    print(f"  ✅ {name}: Form properly rendered")
                else:
                    print(f"  ❌ {name}: Form elements missing")
            else:
                print(f"  ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")

def main():
    """Run all tests."""
    print("🚀 Testing School Management System Registration")
    print("=" * 50)
    
    try:
        # Test basic connectivity
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            sys.exit(1)
        print("✅ Server is running")
        
        # Run tests
        test_page_access()
        test_login_with_sample_accounts()
        test_registration_forms()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        print("\n📝 Manual Testing Instructions:")
        print("1. Open http://127.0.0.1:5000 in your browser")
        print("2. Click 'Register' to see the role selection page")
        print("3. Test each registration form:")
        print("   - Admin: Use code 'ADMIN2024'")
        print("   - Teacher: Fill in all required fields")
        print("   - Student: Select a grade and optional parent")
        print("   - Parent: Fill in contact information")
        print("4. Test login with sample accounts:")
        print("   - admin/admin123 (Admin)")
        print("   - teacher1/teacher123 (Teacher)")
        print("   - student1/student123 (Student)")
        print("   - parent1/parent123 (Parent)")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Flask app is running on port 5000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
