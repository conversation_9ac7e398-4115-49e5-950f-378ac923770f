@echo off
echo 🚀 Starting Flask School Management System...
echo.
echo 📋 Installing dependencies...
python -m pip install flask flask-sqlalchemy flask-login flask-wtf wtforms requests beautifulsoup4
echo.
echo ✅ Dependencies installed
echo.
echo 🌐 Starting server at http://127.0.0.1:5000
echo.
echo 🔑 Test Credentials:
echo   Admin: admin / Admin123!
echo   Teacher: teacher1 / Teacher123!
echo   Student: student1 / Student123!
echo   Parent: parent1 / Parent123!
echo.
echo ⚠️  Press Ctrl+C to stop the server
echo.
python run_server.py
pause
