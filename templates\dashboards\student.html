{% extends "base.html" %}

{% block title %}Student Dashboard{% endblock %}

{% block extra_css %}
<style>
/* Notification Styles */
.notifications-list {
    margin-top: 1em;
}

.notification-item {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1em;
    margin-bottom: 0.5em;
    border-radius: 4px;
}

.notification-item.notification-info {
    border-left-color: #17a2b8;
}

.notification-item.notification-success {
    border-left-color: #28a745;
}

.notification-item.notification-warning {
    border-left-color: #ffc107;
}

.notification-item.notification-danger {
    border-left-color: #dc3545;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5em;
}

.notification-header h4 {
    margin: 0;
    font-size: 1.1em;
}

.notification-date {
    font-size: 0.9em;
    color: #666;
}

.notification-item p {
    margin: 0;
    line-height: 1.4;
}

.more-notifications {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 1em;
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>Student Dashboard</h1>
    <p class="dashboard-welcome">Welcome, {{ student.first_name }} {{ student.last_name }}!</p>

    <div class="dashboard-stats">
        <div class="card">
            <h3>Classes</h3>
            <p class="stat">{{ classes_count }}</p>
        </div>
        <div class="card">
            <h3>Assignments</h3>
            <p class="stat">{{ assignments_count }}</p>
        </div>
        <div class="card">
            <h3>Completed</h3>
            <p class="stat">{{ completed_count }}</p>
        </div>
        <div class="card">
            <h3>Average Grade</h3>
            <p class="stat">{{ average_grade }}</p>
        </div>
    </div>

    <!-- Notifications Section -->
    {% if notifications %}
    <div class="dashboard-section">
        <h2>📢 Notifications</h2>
        <div class="notifications-list">
            {% for notification in notifications[:3] %}
                <div class="notification-item notification-{{ notification.notification_type }}">
                    <div class="notification-header">
                        <h4>{{ notification.title }}</h4>
                        <span class="notification-date">{{ notification.created_at.strftime('%b %d, %Y') }}</span>
                    </div>
                    <p>{{ notification.message }}</p>
                </div>
            {% endfor %}
            {% if notifications|length > 3 %}
                <p class="more-notifications">... and {{ notifications|length - 3 }} more notifications</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <div class="dashboard-section">
        <h2>Upcoming Assignments</h2>
        {% if assignments_count > 0 %}
            <div class="assignment-list">
                {% for assignment in assignments %}
                    {% if not assignment.submission %}
                        <div class="assignment-item">
                            <h3>{{ assignment.title }}</h3>
                            <p><strong>Class:</strong> {{ assignment.class.subject }}</p>
                            <p><strong>Due:</strong> {{ assignment.due_date.strftime('%Y-%m-%d %H:%M') }}</p>
                            <a href="{{ url_for('student.submit_assignment', assignment_id=assignment.id) }}" class="btn btn-sm">Submit</a>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You don't have any assignments yet.</p>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('student.classes') }}" class="btn">View Classes</a>
            <a href="{{ url_for('student.assignments') }}" class="btn">All Assignments</a>
            <a href="{{ url_for('student.profile') }}" class="btn">Edit Profile</a>
        </div>
    </div>
</div>
{% endblock %}