{% extends "base.html" %}

{% block title %}Admin Registration - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <form method="POST" class="login-form">
        {{ form.hidden_tag() }}
        <div class="role-header admin-header">
            <div class="role-icon">👨‍💼</div>
            <h2>Administrator Registration</h2>
            <p class="form-description">Register as a system administrator. Admin accounts are automatically approved.</p>
        </div>

        <div class="form-group">
            {{ form.username.label(class="form-label") }}
            {{ form.username(class="form-control", placeholder="Enter a unique username") }}
            {% if form.username.errors %}
                <div class="form-error">
                    {% for error in form.username.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Username must be at least 4 characters long</small>
        </div>

        <div class="form-group">
            {{ form.email.label(class="form-label") }}
            {{ form.email(class="form-control", placeholder="Enter your email address") }}
            {% if form.email.errors %}
                <div class="form-error">
                    {% for error in form.email.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.admin_code.label(class="form-label") }}
            {{ form.admin_code(class="form-control", placeholder="Enter admin registration code") }}
            {% if form.admin_code.errors %}
                <div class="form-error">
                    {% for error in form.admin_code.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Contact your system administrator for the registration code</small>
        </div>

        <div class="form-group">
            {{ form.password.label(class="form-label") }}
            <div class="password-wrapper">
                {{ form.password(class="form-control", id="password", placeholder="Enter a secure password") }}
                <button type="button" class="show-hide" onclick="togglePassword()">👁️</button>
            </div>
            {% if form.password.errors %}
                <div class="form-error">
                    {% for error in form.password.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Password must be at least 8 characters with uppercase, lowercase, number, and special character</small>
            <div id="password-strength" class="password-strength"></div>
        </div>

        <div class="form-group">
            {{ form.confirm.label(class="form-label") }}
            {{ form.confirm(class="form-control", placeholder="Confirm your password") }}
            {% if form.confirm.errors %}
                <div class="form-error">
                    {% for error in form.confirm.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.submit(class="btn btn-danger animated-btn") }}
        </div>

        <div class="form-footer">
            <p><a href="{{ url_for('auth.register') }}">← Back to role selection</a></p>
            <p><a href="{{ url_for('auth.login') }}">Already have an account? Login here</a></p>
        </div>
    </form>
</div>

<style>
.role-header {
    text-align: center;
    margin-bottom: 2em;
}

.admin-header {
    border-bottom: 3px solid #dc3545;
    padding-bottom: 1em;
}

.role-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.form-description {
    color: var(--secondary);
    margin-bottom: 0;
    font-size: 0.95em;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.85em;
    margin-top: 0.25em;
    display: block;
}

.form-footer {
    text-align: center;
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.form-footer a {
    color: var(--primary);
    text-decoration: none;
    display: block;
    margin: 0.5em 0;
}

.form-footer a:hover {
    text-decoration: underline;
}

.password-wrapper {
    position: relative;
}

.show-hide {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
}

.password-strength {
    margin-top: 0.5em;
}

.password-checks {
    font-size: 0.85em;
    margin-top: 0.5em;
}

.check-pass {
    color: #28a745;
    margin: 0.2em 0;
}

.check-fail {
    color: #dc3545;
    margin: 0.2em 0;
}
</style>

<script>
function togglePassword() {
    var x = document.getElementById("password");
    if (x.type === "password") { x.type = "text"; } else { x.type = "password"; }
}

// Real-time password validation
function validatePassword() {
    const password = document.getElementById('password').value;
    const strengthDiv = document.getElementById('password-strength');

    if (!password) {
        strengthDiv.innerHTML = '';
        return;
    }

    const checks = [
        { test: password.length >= 8, text: 'At least 8 characters' },
        { test: /[A-Z]/.test(password), text: 'Uppercase letter (A-Z)' },
        { test: /[a-z]/.test(password), text: 'Lowercase letter (a-z)' },
        { test: /[0-9]/.test(password), text: 'Number (0-9)' },
        { test: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password), text: 'Special character' }
    ];

    let html = '<div class="password-checks">';
    checks.forEach(check => {
        const icon = check.test ? '✓' : '✗';
        const className = check.test ? 'check-pass' : 'check-fail';
        html += `<div class="${className}">${icon} ${check.text}</div>`;
    });
    html += '</div>';

    strengthDiv.innerHTML = html;
}

// Add event listener when page loads
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', validatePassword);
    }
});
</script>
{% endblock %}
