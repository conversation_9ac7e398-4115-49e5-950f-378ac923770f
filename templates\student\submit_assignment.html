{% extends "base.html" %}

{% block title %}Submit Assignment - {{ assignment.title }}{% endblock %}

{% block content %}
<div class="form-container">
    <h2>Submit Assignment</h2>
    
    <div class="assignment-info-card">
        <h3>{{ assignment.title }}</h3>
        <div class="assignment-details">
            <div class="info-item">
                <strong>Class:</strong> {{ assignment.class_.subject }} ({{ assignment.class_.room }})
            </div>
            <div class="info-item">
                <strong>Due Date:</strong> 
                <span class="due-date">{{ assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            <div class="info-item">
                <strong>Description:</strong>
            </div>
            <div class="assignment-description">
                {{ assignment.description | nl2br | safe }}
            </div>
        </div>
    </div>
    
    <form method="POST" enctype="multipart/form-data" class="submission-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.file.label(class="form-label") }}
            <div class="file-upload-area">
                {{ form.file(class="form-control-file", id="file-input", accept=".pdf,.png,.jpg,.jpeg") }}
                <div class="file-upload-info">
                    <div class="upload-icon">📎</div>
                    <p class="upload-text">Choose a file or drag and drop</p>
                    <p class="upload-requirements">
                        <strong>Accepted formats:</strong> PDF, PNG, JPG, JPEG<br>
                        <strong>Maximum size:</strong> 10MB
                    </p>
                </div>
            </div>
            {% if form.file.errors %}
                <div class="form-error">
                    {% for error in form.file.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <div id="file-preview" class="file-preview" style="display: none;">
                <div class="preview-content">
                    <span id="file-name"></span>
                    <span id="file-size"></span>
                    <button type="button" id="remove-file" class="btn btn-sm btn-outline-danger">Remove</button>
                </div>
            </div>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('student.assignments') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.assignment-info-card {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1.5em;
    margin-bottom: 2em;
    border-left: 4px solid var(--info);
}

.assignment-info-card h3 {
    margin: 0 0 1em 0;
    color: var(--primary);
}

.assignment-details {
    margin-top: 1em;
}

.info-item {
    margin-bottom: 0.75em;
}

.due-date {
    color: var(--warning);
    font-weight: bold;
}

.assignment-description {
    background: var(--background);
    padding: 1em;
    border-radius: 5px;
    border: 1px solid var(--border);
    margin-top: 0.5em;
    line-height: 1.6;
}

.submission-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}

.file-upload-area {
    position: relative;
    border: 2px dashed var(--border);
    border-radius: 8px;
    padding: 2em;
    text-align: center;
    background: var(--card-bg);
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: rgba(0, 123, 255, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: rgba(0, 123, 255, 0.1);
}

.form-control-file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-info {
    pointer-events: none;
}

.upload-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
    color: var(--primary);
}

.upload-text {
    font-size: 1.1em;
    color: var(--text);
    margin: 0.5em 0;
}

.upload-requirements {
    font-size: 0.9em;
    color: var(--secondary);
    margin: 0.5em 0 0 0;
}

.file-preview {
    margin-top: 1em;
    padding: 1em;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid #28a745;
    border-radius: 6px;
}

.preview-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1em;
}

#file-name {
    font-weight: bold;
    color: var(--text);
}

#file-size {
    color: var(--secondary);
    font-size: 0.9em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file-input');
    const uploadArea = document.querySelector('.file-upload-area');
    const filePreview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeButton = document.getElementById('remove-file');

    // File input change handler
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);

    // Remove file handler
    removeButton.addEventListener('click', removeFile);

    function handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            displayFilePreview(file);
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            displayFilePreview(files[0]);
        }
    }

    function displayFilePreview(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        filePreview.style.display = 'block';

        // Validate file type and size
        const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
            alert('Please select a PDF or image file (PNG, JPG, JPEG)');
            removeFile();
            return;
        }

        if (file.size > maxSize) {
            alert('File size must be less than 10MB');
            removeFile();
            return;
        }
    }

    function removeFile() {
        fileInput.value = '';
        filePreview.style.display = 'none';
        fileName.textContent = '';
        fileSize.textContent = '';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
{% endblock %}
