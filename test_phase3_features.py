#!/usr/bin/env python3
"""
Test script for Phase 3 features of the Flask School Management System.
Tests enhanced teacher assignment management and admin notification system.
"""

import requests
from bs4 import BeautifulSoup
import sys
import json

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    response = session.get(url)
    if response.status_code != 200:
        return None
    
    soup = BeautifulSoup(response.content, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    return csrf_input['value'] if csrf_input else None

def login_as_role(session, role):
    """Login as a specific role and return success status."""
    credentials = {
        'admin': ('admin', 'Admin123!'),
        'teacher': ('teacher1', 'Teacher123!'),
        'student': ('student1', 'Student123!')
    }
    
    if role not in credentials:
        return False
    
    username, password = credentials[role]
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        return False
    
    data = {
        'username': username,
        'password': password,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/login", data=data, allow_redirects=False)
    return response.status_code in [302, 303]

def test_enhanced_teacher_features():
    """Test enhanced teacher assignment management features."""
    print("👨‍🏫 Testing Enhanced Teacher Features...")
    
    session = requests.Session()
    if not login_as_role(session, 'teacher'):
        print("  ❌ Failed to login as teacher")
        return
    
    # Test enhanced assignments page
    try:
        response = session.get(f"{BASE_URL}/teacher/assignments")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for enhanced assignment statistics
            stat_items = soup.find_all('div', class_='info-item')
            if len(stat_items) >= 3:
                print("  ✅ Enhanced assignment statistics displayed")
            else:
                print("  ❌ Assignment statistics missing")
            
            # Check for grading badges
            grading_badges = soup.find_all('span', class_='badge')
            if grading_badges:
                print("  ✅ Grading status badges found")
            else:
                print("  ❌ Grading status badges missing")
                
        else:
            print(f"  ❌ Assignments page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Teacher assignments test error: {e}")
    
    # Test class management page
    try:
        response = session.get(f"{BASE_URL}/teacher/manage_classes")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for class management features
            class_cards = soup.find_all('div', class_='class-card')
            stat_cards = soup.find_all('div', class_='stat-card')
            
            if stat_cards:
                print("  ✅ Class management overview statistics found")
            else:
                print("  ❌ Class management statistics missing")
                
            if class_cards or soup.find('div', class_='empty-state'):
                print("  ✅ Class management interface working")
            else:
                print("  ❌ Class management interface missing")
                
        else:
            print(f"  ❌ Class management page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Class management test error: {e}")

def test_admin_notification_system():
    """Test admin notification management system."""
    print("\n📢 Testing Admin Notification System...")
    
    session = requests.Session()
    if not login_as_role(session, 'admin'):
        print("  ❌ Failed to login as admin")
        return
    
    # Test notifications management page
    try:
        response = session.get(f"{BASE_URL}/admin/notifications")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for notification management interface
            notification_cards = soup.find_all('div', class_='notification-card')
            stat_cards = soup.find_all('div', class_='stat-card')
            
            if stat_cards:
                print("  ✅ Notification statistics displayed")
            else:
                print("  ❌ Notification statistics missing")
            
            if notification_cards or soup.find('div', class_='empty-state'):
                print("  ✅ Notification management interface working")
            else:
                print("  ❌ Notification management interface missing")
            
            # Check for create notification button
            create_button = soup.find('a', href=lambda x: x and 'create_notification' in x)
            if create_button:
                print("  ✅ Create notification button found")
            else:
                print("  ❌ Create notification button missing")
                
        else:
            print(f"  ❌ Notifications page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Notification management test error: {e}")
    
    # Test notification creation page
    try:
        response = session.get(f"{BASE_URL}/admin/notifications/create")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for notification form elements
            title_field = soup.find('input', {'name': 'title'})
            message_field = soup.find('textarea', {'name': 'message'})
            type_field = soup.find('select', {'name': 'notification_type'})
            role_field = soup.find('select', {'name': 'recipient_role'})
            
            form_elements = [title_field, message_field, type_field, role_field]
            if all(form_elements):
                print("  ✅ Notification creation form complete")
            else:
                print("  ❌ Notification creation form incomplete")
            
            # Check for preview functionality
            preview_section = soup.find('div', class_='preview-section')
            if preview_section:
                print("  ✅ Notification preview functionality found")
            else:
                print("  ❌ Notification preview functionality missing")
                
        else:
            print(f"  ❌ Create notification page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Notification creation test error: {e}")

def test_admin_system_overview():
    """Test admin system overview page."""
    print("\n📊 Testing Admin System Overview...")
    
    session = requests.Session()
    if not login_as_role(session, 'admin'):
        print("  ❌ Failed to login as admin")
        return
    
    try:
        response = session.get(f"{BASE_URL}/admin/overview")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for system statistics
            stat_cards = soup.find_all('div', class_='stat-card')
            if len(stat_cards) >= 4:
                print("  ✅ System statistics displayed")
            else:
                print("  ❌ System statistics incomplete")
            
            # Check for grade distribution
            grade_chart = soup.find('div', class_='grade-chart')
            if grade_chart:
                print("  ✅ Grade distribution chart found")
            else:
                print("  ❌ Grade distribution chart missing")
            
            # Check for recent submissions table
            submissions_table = soup.find('table', class_='table')
            if submissions_table:
                print("  ✅ Recent submissions table found")
            else:
                print("  ❌ Recent submissions table missing")
            
            # Check for quick actions
            action_cards = soup.find_all('a', class_='action-card')
            if len(action_cards) >= 3:
                print("  ✅ Quick action cards found")
            else:
                print("  ❌ Quick action cards missing")
                
        else:
            print(f"  ❌ System overview page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ System overview test error: {e}")

def test_enhanced_grading_system():
    """Test the enhanced grading system."""
    print("\n📝 Testing Enhanced Grading System...")
    
    session = requests.Session()
    if not login_as_role(session, 'teacher'):
        print("  ❌ Failed to login as teacher")
        return
    
    # Test assignment detail page with grading interface
    try:
        # First get assignments list to find an assignment
        response = session.get(f"{BASE_URL}/teacher/assignments")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for assignment detail links
            detail_links = soup.find_all('a', href=lambda x: x and '/teacher/assignment/' in x)
            
            if detail_links:
                # Test the first assignment detail page
                assignment_url = detail_links[0]['href']
                response = session.get(f"{BASE_URL}{assignment_url}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Check for enhanced assignment detail features
                    student_submissions = soup.find_all('div', class_='student-item')
                    if student_submissions:
                        print("  ✅ Enhanced assignment detail with student list")
                    else:
                        print("  ❌ Enhanced assignment detail missing")
                    
                    # Check for grading links
                    grade_links = soup.find_all('a', href=lambda x: x and '/grade' in x)
                    if grade_links:
                        print("  ✅ Grading interface links found")
                    else:
                        print("  ❌ Grading interface links missing")
                        
                else:
                    print(f"  ❌ Assignment detail page: Status {response.status_code}")
            else:
                print("  ℹ️ No assignments available for grading test")
        else:
            print(f"  ❌ Teacher assignments page: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Enhanced grading test error: {e}")

def test_dashboard_enhancements():
    """Test dashboard enhancements for all roles."""
    print("\n🏠 Testing Dashboard Enhancements...")
    
    roles = ['admin', 'teacher']
    
    for role in roles:
        session = requests.Session()
        if not login_as_role(session, role):
            print(f"  ❌ Failed to login as {role}")
            continue
        
        try:
            response = session.get(f"{BASE_URL}/{role}/dashboard")
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for enhanced action buttons
                action_buttons = soup.find_all('a', class_='btn')
                enhanced_features = 0
                
                for button in action_buttons:
                    text = button.get_text().lower()
                    if any(keyword in text for keyword in ['overview', 'notification', 'management', 'center']):
                        enhanced_features += 1
                
                if enhanced_features >= 2:
                    print(f"  ✅ {role.title()} dashboard enhanced with new features")
                else:
                    print(f"  ❌ {role.title()} dashboard enhancements missing")
                    
            else:
                print(f"  ❌ {role.title()} dashboard: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {role.title()} dashboard test error: {e}")

def main():
    """Run all Phase 3 feature tests."""
    print("🚀 Testing Phase 3 Features - Teacher Management & Admin Notifications")
    print("=" * 80)
    
    try:
        # Test basic connectivity
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            sys.exit(1)
        print("✅ Server is running")
        
        # Run Phase 3 tests
        test_enhanced_teacher_features()
        test_admin_notification_system()
        test_admin_system_overview()
        test_enhanced_grading_system()
        test_dashboard_enhancements()
        
        print("\n" + "=" * 80)
        print("✅ Phase 3 Feature Testing Complete!")
        print("\n📋 PHASE 3 FEATURES IMPLEMENTED:")
        print("✅ Enhanced teacher assignment management with statistics")
        print("✅ Advanced grading system with timestamps and feedback")
        print("✅ Comprehensive class management interface")
        print("✅ Admin notification system with targeting")
        print("✅ System overview with analytics and grade distribution")
        print("✅ Enhanced dashboards for all user roles")
        print("✅ Database schema improvements and migrations")
        
        print("\n🌐 TEST THE NEW FEATURES:")
        print("1. Admin Features:")
        print("   - Login as admin / Admin123!")
        print("   - Visit: http://127.0.0.1:5000/admin/dashboard")
        print("   - Test: System Overview, Notification Management")
        print("\n2. Teacher Features:")
        print("   - Login as teacher1 / Teacher123!")
        print("   - Visit: http://127.0.0.1:5000/teacher/dashboard")
        print("   - Test: Class Management, Enhanced Assignment Center")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Flask app is running on port 5000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
