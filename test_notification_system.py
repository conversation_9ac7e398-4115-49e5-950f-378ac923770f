#!/usr/bin/env python3
"""
Comprehensive test script for the notification system.
Tests notification creation, visibility, and dashboard integration.
"""

import requests
from bs4 import BeautifulSoup
import time

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    try:
        response = session.get(url, timeout=10)
        if response.status_code != 200:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        return csrf_input['value'] if csrf_input else None
    except:
        return None

def login_as_role(role, username, password):
    """Login as a specific role and return session."""
    session = requests.Session()
    
    # Get CSRF token
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        print(f"❌ Could not get CSRF token for {role}")
        return None
    
    # Login
    login_data = {
        'username': username,
        'password': password,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    if response.status_code in [302, 303]:
        print(f"✅ {role} login successful")
        return session
    else:
        print(f"❌ {role} login failed: {response.status_code}")
        return None

def create_test_notification(session, title, message, recipient_role, notification_type="info"):
    """Create a test notification as admin."""
    print(f"\n🔧 Creating notification: '{title}' for {recipient_role}")
    
    # Get CSRF token for notification creation
    csrf_token = get_csrf_token(session, f"{BASE_URL}/admin/notifications/create")
    if not csrf_token:
        print("❌ Could not get CSRF token for notification creation")
        return False
    
    # Create notification
    notification_data = {
        'title': title,
        'message': message,
        'notification_type': notification_type,
        'recipient_role': recipient_role,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/admin/notifications/create", data=notification_data, allow_redirects=False)
    
    if response.status_code in [302, 303]:
        print(f"✅ Notification '{title}' created successfully")
        return True
    else:
        print(f"❌ Notification creation failed: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        return False

def check_notifications_in_dashboard(session, role, expected_count=None):
    """Check if notifications appear in user dashboard."""
    print(f"\n🔍 Checking notifications in {role} dashboard...")
    
    try:
        response = session.get(f"{BASE_URL}/{role}/dashboard", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Dashboard access failed: {response.status_code}")
            return False
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for notification sections
        notification_sections = soup.find_all('div', class_='notifications-list')
        notification_items = soup.find_all('div', class_='notification-item')
        
        print(f"Found {len(notification_sections)} notification sections")
        print(f"Found {len(notification_items)} notification items")
        
        if len(notification_items) > 0:
            print("✅ Notifications found in dashboard")
            for i, item in enumerate(notification_items[:3]):
                title_elem = item.find('h4')
                title = title_elem.get_text().strip() if title_elem else "No title"
                print(f"  - Notification {i+1}: {title}")
            return True
        else:
            print("❌ No notifications found in dashboard")
            
            # Debug: check if notification section exists but is empty
            if notification_sections:
                print("📝 Notification section exists but is empty")
            else:
                print("📝 No notification section found in template")
            
            return False
            
    except Exception as e:
        print(f"❌ Error checking dashboard: {e}")
        return False

def check_admin_notification_list(session):
    """Check if notifications appear in admin notification management."""
    print(f"\n🔍 Checking admin notification list...")
    
    try:
        response = session.get(f"{BASE_URL}/admin/notifications", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Admin notifications access failed: {response.status_code}")
            return False
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for notification cards
        notification_cards = soup.find_all('div', class_='notification-card')
        
        print(f"Found {len(notification_cards)} notifications in admin list")
        
        if len(notification_cards) > 0:
            print("✅ Notifications found in admin list")
            for i, card in enumerate(notification_cards[:3]):
                title_elem = card.find('h3')
                title = title_elem.get_text().strip() if title_elem else "No title"
                print(f"  - Notification {i+1}: {title}")
            return True
        else:
            print("❌ No notifications found in admin list")
            return False
            
    except Exception as e:
        print(f"❌ Error checking admin notifications: {e}")
        return False

def main():
    """Run comprehensive notification system test."""
    print("🧪 Comprehensive Notification System Test")
    print("=" * 50)
    
    # Test credentials
    test_users = [
        ('admin', 'admin', 'Admin123!'),
        ('teacher', 'teacher1', 'Teacher123!'),
        ('student', 'student1', 'Student123!'),
        ('parent', 'parent1', 'Parent123!')
    ]
    
    # Step 1: Login as admin and create test notifications
    print("\n📝 Step 1: Creating Test Notifications")
    admin_session = login_as_role('admin', 'admin', 'Admin123!')
    if not admin_session:
        print("❌ Cannot proceed without admin access")
        return False
    
    # Create different types of notifications
    test_notifications = [
        ("All Users Test", "This notification should appear for all users", "all", "info"),
        ("Teachers Only", "This is for teachers only", "teacher", "warning"),
        ("Students Only", "Important student announcement", "student", "success"),
        ("Parents Only", "Parent meeting notification", "parent", "danger")
    ]
    
    created_count = 0
    for title, message, role, ntype in test_notifications:
        if create_test_notification(admin_session, title, message, role, ntype):
            created_count += 1
        time.sleep(1)  # Small delay between creations
    
    print(f"\n📊 Created {created_count}/{len(test_notifications)} notifications")
    
    # Step 2: Check admin notification list
    print("\n📝 Step 2: Checking Admin Notification List")
    check_admin_notification_list(admin_session)
    
    # Step 3: Test notification visibility for each role
    print("\n📝 Step 3: Testing Notification Visibility")
    
    results = {}
    for role, username, password in test_users:
        print(f"\n--- Testing {role.title()} Dashboard ---")
        session = login_as_role(role, username, password)
        if session:
            results[role] = check_notifications_in_dashboard(session, role)
        else:
            results[role] = False
    
    # Step 4: Summary
    print("\n" + "=" * 50)
    print("📊 NOTIFICATION SYSTEM TEST RESULTS")
    print("=" * 50)
    
    print(f"Notifications Created: {created_count}/{len(test_notifications)}")
    print("Dashboard Visibility:")
    for role, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {role.title()}: {status}")
    
    total_tests = len(results) + 1  # +1 for creation test
    passed_tests = sum(results.values()) + (1 if created_count > 0 else 0)
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - Notification system working correctly!")
        return True
    else:
        print("⚠️ Some tests failed - check issues above")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
