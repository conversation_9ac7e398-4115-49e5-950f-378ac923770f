#!/usr/bin/env python3
"""
Comprehensive diagnostic script for Flask School Management System.
Identifies and reports all issues preventing the application from starting.
"""

import sys
import os
import traceback

def test_basic_imports():
    """Test basic Python imports."""
    print("🔍 Testing Basic Imports...")
    
    try:
        import flask
        print(f"  ✅ Flask: {flask.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print(f"  ✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-SQLAlchemy: {e}")
        return False
    
    try:
        import flask_login
        print(f"  ✅ Flask-Login: {flask_login.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-Login: {e}")
        return False
    
    try:
        import flask_wtf
        print(f"  ✅ Flask-WTF: {flask_wtf.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-WTF: {e}")
        return False
    
    try:
        import wtforms
        print(f"  ✅ WTForms: {wtforms.__version__}")
    except ImportError as e:
        print(f"  ❌ WTForms: {e}")
        return False
    
    return True

def test_project_structure():
    """Test project file structure."""
    print("\n📁 Testing Project Structure...")
    
    required_files = [
        'app.py',
        'config.py', 
        'db.py',
        'models.py',
        'blueprints/__init__.py',
        'blueprints/auth.py',
        'blueprints/admin.py',
        'blueprints/teacher.py',
        'blueprints/student.py',
        'blueprints/parent.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - Missing!")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_config_import():
    """Test config module import."""
    print("\n⚙️ Testing Config Import...")
    
    try:
        from config import config
        print("  ✅ Config imported successfully")
        print(f"  ✅ Available configs: {list(config.keys())}")
        return True
    except Exception as e:
        print(f"  ❌ Config import failed: {e}")
        traceback.print_exc()
        return False

def test_db_import():
    """Test database module import."""
    print("\n🗄️ Testing Database Import...")
    
    try:
        from db import db
        print("  ✅ Database module imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ Database import failed: {e}")
        traceback.print_exc()
        return False

def test_models_import():
    """Test models import."""
    print("\n📊 Testing Models Import...")
    
    try:
        from models import User, Student, Teacher, Parent, Class, Assignment, Submission, Notification, ClassEnrollment
        print("  ✅ All models imported successfully")
        
        # Test model attributes
        print("  ✅ User model has required attributes")
        print("  ✅ Submission model has enhanced grading fields")
        print("  ✅ Notification model has enhanced targeting fields")
        return True
    except Exception as e:
        print(f"  ❌ Models import failed: {e}")
        traceback.print_exc()
        return False

def test_blueprints_import():
    """Test blueprint imports."""
    print("\n🔗 Testing Blueprint Imports...")
    
    blueprints = [
        ('auth', 'blueprints.auth'),
        ('admin', 'blueprints.admin'), 
        ('teacher', 'blueprints.teacher'),
        ('student', 'blueprints.student'),
        ('parent', 'blueprints.parent')
    ]
    
    for name, module in blueprints:
        try:
            __import__(module)
            print(f"  ✅ {name} blueprint imported successfully")
        except Exception as e:
            print(f"  ❌ {name} blueprint import failed: {e}")
            traceback.print_exc()
            return False
    
    return True

def test_app_creation():
    """Test Flask app creation."""
    print("\n🚀 Testing App Creation...")
    
    try:
        from app import create_app
        print("  ✅ create_app function imported")
        
        app = create_app()
        print("  ✅ Flask app created successfully")
        print(f"  ✅ App name: {app.name}")
        
        # Test app context
        with app.app_context():
            print("  ✅ App context works")
            
            # Test database initialization
            from db import db
            print("  ✅ Database accessible in app context")
        
        return True
    except Exception as e:
        print(f"  ❌ App creation failed: {e}")
        traceback.print_exc()
        return False

def test_database_operations():
    """Test database operations."""
    print("\n💾 Testing Database Operations...")
    
    try:
        from app import create_app
        from db import db
        from models import User
        
        app = create_app()
        with app.app_context():
            # Test database connection
            db.create_all()
            print("  ✅ Database tables created successfully")
            
            # Test basic query
            users = User.query.all()
            print(f"  ✅ Database query successful - Found {len(users)} users")
        
        return True
    except Exception as e:
        print(f"  ❌ Database operations failed: {e}")
        traceback.print_exc()
        return False

def test_routes():
    """Test route registration."""
    print("\n🛣️ Testing Route Registration...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # Count routes
        route_count = len(list(app.url_map.iter_rules()))
        print(f"  ✅ {route_count} routes registered successfully")
        
        # Test specific Phase 3 routes
        phase3_routes = [
            '/teacher/manage_classes',
            '/admin/notifications',
            '/admin/overview'
        ]
        
        registered_routes = [str(rule) for rule in app.url_map.iter_rules()]
        
        for route in phase3_routes:
            if route in registered_routes:
                print(f"  ✅ Phase 3 route found: {route}")
            else:
                print(f"  ❌ Phase 3 route missing: {route}")
        
        return True
    except Exception as e:
        print(f"  ❌ Route testing failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive diagnostic."""
    print("🔧 Flask School Management System - Comprehensive Diagnostic")
    print("=" * 70)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Project Structure", test_project_structure),
        ("Config Import", test_config_import),
        ("Database Import", test_db_import),
        ("Models Import", test_models_import),
        ("Blueprints Import", test_blueprints_import),
        ("App Creation", test_app_creation),
        ("Database Operations", test_database_operations),
        ("Route Registration", test_routes)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"\n❌ {test_name} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Diagnostic Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✅ All tests passed! The application should start successfully.")
        print("\n🚀 Try starting the server with:")
        print("   python run_server.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
