"""
Sample Data Generator for School Management System
-------------------------------------------------
This script creates comprehensive sample data for the School Management System including:
- Users (Admin, Teachers, Students, Parents)
- Teacher, Student, and Parent profiles
- Classes with schedules
- Student enrollments
- Assignments with due dates
- Student submissions (some graded, some pending)
- Notifications
- Pending user approvals for testing
"""

import os
import sys
from datetime import datetime, timedelta
from app import create_app
from models import db, User, Teacher, Student, Parent, Class, ClassEnrollment, Assignment, Submission, Notification

def create_sample_data():
    """Create comprehensive sample data for the School Management System."""
    print("Creating comprehensive sample data...")

    # Create application context
    app = create_app()
    with app.app_context():
        # Clear existing data
        print("Clearing existing data...")
        db.drop_all()
        db.create_all()

        print("Creating admin user...")
        # Create Admin User (always exists)
        admin = User(username='admin', email='<EMAIL>', role='admin', is_approved=True)
        admin.set_password('Admin123!')
        db.session.add(admin)
        db.session.commit()

        # Create teachers
        print("Creating teachers...")
        teachers_data = [
            {
                'username': 'teacher1',
                'email': '<EMAIL>',
                'password': 'Teacher123!',
                'first_name': 'John',
                'last_name': 'Smith',
                'subjects': 'Mathematics, Physics',
                'years_experience': 8,
                'qualifications': 'M.S. in Mathematics, B.S. in Physics, Teaching Certificate'
            },
            {
                'username': 'teacher2',
                'email': '<EMAIL>',
                'password': 'Teacher123!',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'subjects': 'English, Literature, History',
                'years_experience': 12,
                'qualifications': 'M.A. in English Literature, B.A. in History, TESOL Certification'
            },
            {
                'username': 'teacher3',
                'email': '<EMAIL>',
                'password': 'Teacher123!',
                'first_name': 'Michael',
                'last_name': 'Brown',
                'subjects': 'Science, Chemistry, Biology',
                'years_experience': 5,
                'qualifications': 'M.S. in Chemistry, B.S. in Biology, Science Teaching License'
            }
        ]

        teacher_objects = []
        for teacher_data in teachers_data:
            # Create user
            user = User(
                username=teacher_data['username'],
                email=teacher_data['email'],
                role='teacher',
                is_approved=True
            )
            user.set_password(teacher_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create teacher profile
            teacher = Teacher(
                user_id=user.id,
                first_name=teacher_data['first_name'],
                last_name=teacher_data['last_name'],
                subjects=teacher_data['subjects'],
                years_experience=teacher_data['years_experience'],
                qualifications=teacher_data['qualifications']
            )
            db.session.add(teacher)
            teacher_objects.append(teacher)

        # Create parents
        print("Creating parents...")
        parents_data = [
            {
                'username': 'parent1',
                'email': '<EMAIL>',
                'password': 'Parent123!',
                'first_name': 'Michael',
                'last_name': 'Brown',
                'phone': '555-0101',
                'relationship_type': 'Father',
                'children_info': '[{"name": "Emma Brown", "grade": "9"}, {"name": "Liam Brown", "grade": "11"}]'
            },
            {
                'username': 'parent2',
                'email': '<EMAIL>',
                'password': 'Parent123!',
                'first_name': 'Lisa',
                'last_name': 'Davis',
                'phone': '555-0102',
                'relationship_type': 'Mother',
                'children_info': '[{"name": "James Davis", "grade": "10"}]'
            },
            {
                'username': 'parent3',
                'email': '<EMAIL>',
                'password': 'Parent123!',
                'first_name': 'Robert',
                'last_name': 'Wilson',
                'phone': '555-0103',
                'relationship_type': 'Father',
                'children_info': '[{"name": "Olivia Wilson", "grade": "9"}]'
            }
        ]

        parent_objects = []
        for parent_data in parents_data:
            # Create user
            user = User(
                username=parent_data['username'],
                email=parent_data['email'],
                role='parent',
                is_approved=True
            )
            user.set_password(parent_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create parent profile
            parent = Parent(
                user_id=user.id,
                first_name=parent_data['first_name'],
                last_name=parent_data['last_name'],
                phone=parent_data['phone'],
                relationship_type=parent_data['relationship_type'],
                children_info=parent_data['children_info']
            )
            db.session.add(parent)
            parent_objects.append(parent)

        # Create students
        print("Creating students...")
        students_data = [
            {
                'username': 'student1',
                'email': '<EMAIL>',
                'password': 'Student123!',
                'first_name': 'Emma',
                'last_name': 'Brown',
                'dob': datetime(2008, 5, 15).date(),
                'grade': '9th Grade',
                'parent': parent_objects[0]
            },
            {
                'username': 'student2',
                'email': '<EMAIL>',
                'password': 'Student123!',
                'first_name': 'James',
                'last_name': 'Davis',
                'dob': datetime(2007, 8, 22).date(),
                'grade': '10th Grade',
                'parent': parent_objects[1]
            },
            {
                'username': 'student3',
                'email': '<EMAIL>',
                'password': 'Student123!',
                'first_name': 'Olivia',
                'last_name': 'Wilson',
                'dob': datetime(2008, 12, 3).date(),
                'grade': '9th Grade',
                'parent': parent_objects[2]
            },
            {
                'username': 'student4',
                'email': '<EMAIL>',
                'password': 'Student123!',
                'first_name': 'Liam',
                'last_name': 'Brown',
                'dob': datetime(2006, 3, 10).date(),
                'grade': '11th Grade',
                'parent': parent_objects[0]
            }
        ]

        student_objects = []
        for student_data in students_data:
            # Create user
            user = User(
                username=student_data['username'],
                email=student_data['email'],
                role='student',
                is_approved=True
            )
            user.set_password(student_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create student profile
            student = Student(
                user_id=user.id,
                first_name=student_data['first_name'],
                last_name=student_data['last_name'],
                dob=student_data['dob'],
                grade=student_data['grade'],
                parent_id=student_data['parent'].id
            )
            db.session.add(student)
            student_objects.append(student)

        # Create pending users for testing approval system
        print("Creating pending users...")
        pending_teacher = User(username='pending_teacher', email='<EMAIL>', role='teacher', is_approved=False)
        pending_teacher.set_password('Pending123!')
        db.session.add(pending_teacher)

        pending_student = User(username='pending_student', email='<EMAIL>', role='student', is_approved=False)
        pending_student.set_password('Pending123!')
        db.session.add(pending_student)

        db.session.commit()

        # Create classes
        print("Creating classes...")
        classes_data = [
            {
                'teacher': teacher_objects[0],
                'subject': 'Algebra I',
                'schedule_time': datetime(2024, 1, 15, 9, 0),
                'room': 'Room 101'
            },
            {
                'teacher': teacher_objects[0],
                'subject': 'Physics',
                'schedule_time': datetime(2024, 1, 15, 11, 0),
                'room': 'Lab A'
            },
            {
                'teacher': teacher_objects[1],
                'subject': 'English Literature',
                'schedule_time': datetime(2024, 1, 16, 10, 0),
                'room': 'Room 203'
            },
            {
                'teacher': teacher_objects[1],
                'subject': 'World History',
                'schedule_time': datetime(2024, 1, 17, 14, 0),
                'room': 'Room 205'
            },
            {
                'teacher': teacher_objects[2],
                'subject': 'Chemistry',
                'schedule_time': datetime(2024, 1, 18, 13, 0),
                'room': 'Lab B'
            }
        ]

        class_objects = []
        for class_data in classes_data:
            class_obj = Class(
                teacher_id=class_data['teacher'].id,
                subject=class_data['subject'],
                schedule_time=class_data['schedule_time'],
                room=class_data['room']
            )
            db.session.add(class_obj)
            db.session.flush()  # Flush to get the class ID
            class_objects.append(class_obj)

        # Create enrollments
        print("Creating enrollments...")
        enrollments_data = [
            # Emma Brown (student1) - 9th Grade
            {'student': student_objects[0], 'class': class_objects[0]},  # Algebra I
            {'student': student_objects[0], 'class': class_objects[2]},  # English Literature

            # James Davis (student2) - 10th Grade
            {'student': student_objects[1], 'class': class_objects[0]},  # Algebra I
            {'student': student_objects[1], 'class': class_objects[1]},  # Physics
            {'student': student_objects[1], 'class': class_objects[3]},  # World History

            # Olivia Wilson (student3) - 9th Grade
            {'student': student_objects[2], 'class': class_objects[0]},  # Algebra I
            {'student': student_objects[2], 'class': class_objects[2]},  # English Literature

            # Liam Brown (student4) - 11th Grade
            {'student': student_objects[3], 'class': class_objects[1]},  # Physics
            {'student': student_objects[3], 'class': class_objects[4]},  # Chemistry
            {'student': student_objects[3], 'class': class_objects[3]},  # World History
        ]

        for enrollment_data in enrollments_data:
            enrollment = ClassEnrollment(
                student_id=enrollment_data['student'].id,
                class_id=enrollment_data['class'].id
            )
            db.session.add(enrollment)

        # Create assignments
        print("Creating assignments...")
        now = datetime.now()
        assignments_data = [
            {
                'class': class_objects[0],  # Algebra I
                'title': 'Quadratic Equations Homework',
                'description': 'Complete exercises 1-20 from Chapter 5. Show all work and explain your reasoning for each problem.',
                'due_date': now + timedelta(days=7)
            },
            {
                'class': class_objects[0],  # Algebra I
                'title': 'Algebra Quiz Preparation',
                'description': 'Study guide for upcoming quiz on linear equations and graphing.',
                'due_date': now + timedelta(days=3)
            },
            {
                'class': class_objects[1],  # Physics
                'title': 'Newton\'s Laws Lab Report',
                'description': 'Complete the lab report based on our Newton\'s Laws experiment. Include hypothesis, procedure, results, and conclusion.',
                'due_date': now + timedelta(days=5)
            },
            {
                'class': class_objects[2],  # English Literature
                'title': 'Shakespeare Essay',
                'description': 'Write a 500-word essay analyzing the themes in Romeo and Juliet. Focus on the theme of fate vs. free will.',
                'due_date': now + timedelta(days=10)
            },
            {
                'class': class_objects[3],  # World History
                'title': 'Ancient Civilizations Project',
                'description': 'Research project on ancient civilizations. Choose one civilization and create a presentation.',
                'due_date': now - timedelta(days=2)  # Past due for testing
            },
            {
                'class': class_objects[4],  # Chemistry
                'title': 'Chemical Reactions Lab',
                'description': 'Complete the chemical reactions lab worksheet and submit your observations.',
                'due_date': now + timedelta(days=14)
            }
        ]

        assignment_objects = []
        for assignment_data in assignments_data:
            assignment = Assignment(
                class_id=assignment_data['class'].id,
                title=assignment_data['title'],
                description=assignment_data['description'],
                due_date=assignment_data['due_date']
            )
            db.session.add(assignment)
            db.session.flush()  # Flush to get the assignment ID
            assignment_objects.append(assignment)

        # Commit to ensure assignments are saved
        db.session.commit()

        # Create submissions
        print("Creating submissions...")
        submissions_data = [
            {
                'student': student_objects[0],  # Emma
                'assignment': assignment_objects[0],  # Quadratic Equations
                'file_path': 'sample_submission.pdf',
                'grade': 'A-',
                'feedback': 'Excellent work! Your solutions are clear and well-explained.'
            },
            {
                'student': student_objects[1],  # James
                'assignment': assignment_objects[0],  # Quadratic Equations
                'file_path': 'sample_submission.pdf',
                'grade': None,  # Not graded yet
                'feedback': None
            },
            {
                'student': student_objects[2],  # Olivia
                'assignment': assignment_objects[3],  # Shakespeare Essay
                'file_path': 'sample_submission.pdf',
                'grade': 'B+',
                'feedback': 'Good analysis of the themes. Consider expanding on your examples.'
            },
            {
                'student': student_objects[3],  # Liam
                'assignment': assignment_objects[2],  # Newton's Laws Lab
                'file_path': 'sample_submission.pdf',
                'grade': 'A',
                'feedback': 'Outstanding lab report with clear methodology and conclusions.'
            }
        ]

        for submission_data in submissions_data:
            submission = Submission(
                student_id=submission_data['student'].id,
                assignment_id=submission_data['assignment'].id,
                file_path=submission_data['file_path'],
                grade=submission_data['grade'],
                feedback=submission_data['feedback']
            )
            db.session.add(submission)

        # Create notifications
        print("Creating notifications...")
        notifications_data = [
            {
                'sender': admin,
                'recipient_role': 'all',
                'message': 'Welcome to the new school management system! Please update your profiles.'
            },
            {
                'sender': teacher_objects[0].user,
                'recipient_role': 'student',
                'message': 'Reminder: Math quiz next Friday. Please review chapters 4-6.'
            },
            {
                'sender': admin,
                'recipient_role': 'parent',
                'message': 'Parent-teacher conferences will be held next month. Sign-up sheets are available.'
            }
        ]

        for notification_data in notifications_data:
            notification = Notification(
                sender_id=notification_data['sender'].id,
                recipient_role=notification_data['recipient_role'],
                message=notification_data['message']
            )
            db.session.add(notification)

        # Commit all changes
        db.session.commit()

        print("\n" + "="*60)
        print("SAMPLE DATA CREATED SUCCESSFULLY!")
        print("="*60)
        print("\nTEST LOGIN CREDENTIALS:")
        print("-" * 30)
        print("ADMIN:")
        print("  Username: admin")
        print("  Password: Admin123!")
        print("  Email: <EMAIL>")
        print()
        print("TEACHERS:")
        print("  Username: teacher1")
        print("  Password: Teacher123!")
        print("  Email: <EMAIL>")
        print("  Name: John Smith (Math, Physics) - 8 years experience")
        print()
        print("  Username: teacher2")
        print("  Password: Teacher123!")
        print("  Email: <EMAIL>")
        print("  Name: Sarah Johnson (English, History) - 12 years experience")
        print()
        print("  Username: teacher3")
        print("  Password: Teacher123!")
        print("  Email: <EMAIL>")
        print("  Name: Michael Brown (Science, Chemistry) - 5 years experience")
        print()
        print("STUDENTS:")
        print("  Username: student1")
        print("  Password: Student123!")
        print("  Email: <EMAIL>")
        print("  Name: Emma Brown (9th Grade)")
        print()
        print("  Username: student2")
        print("  Password: Student123!")
        print("  Email: <EMAIL>")
        print("  Name: James Davis (10th Grade)")
        print()
        print("  Username: student3")
        print("  Password: Student123!")
        print("  Email: <EMAIL>")
        print("  Name: Olivia Wilson (9th Grade)")
        print()
        print("  Username: student4")
        print("  Password: Student123!")
        print("  Email: <EMAIL>")
        print("  Name: Liam Brown (11th Grade)")
        print()
        print("PARENTS:")
        print("  Username: parent1")
        print("  Password: Parent123!")
        print("  Email: <EMAIL>")
        print("  Name: Michael Brown (Father, 2 children)")
        print()
        print("  Username: parent2")
        print("  Password: Parent123!")
        print("  Email: <EMAIL>")
        print("  Name: Lisa Davis (Mother, 1 child)")
        print()
        print("  Username: parent3")
        print("  Password: Parent123!")
        print("  Email: <EMAIL>")
        print("  Name: Robert Wilson (Father, 1 child)")
        print()
        print("PENDING APPROVAL (for testing):")
        print("  Username: pending_teacher")
        print("  Password: Pending123!")
        print("  Email: <EMAIL>")
        print()
        print("  Username: pending_student")
        print("  Password: Pending123!")
        print("  Email: <EMAIL>")
        print()
        print("DATABASE FEATURES INCLUDED:")
        print("- User accounts with role-based access")
        print("- Teacher profiles with subjects")
        print("- Student profiles with grades and parent links")
        print("- Parent profiles with phone numbers")
        print("- Classes with schedules and room assignments")
        print("- Student enrollments in classes")
        print("- Assignments with due dates (including past due)")
        print("- Student submissions (some graded, some pending)")
        print("- System notifications")
        print("- Pending user approvals for admin testing")
        print()
        print("You can now test all features of the system!")
        print("="*60)

if __name__ == '__main__':
    create_sample_data()
