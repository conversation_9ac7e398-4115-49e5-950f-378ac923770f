{% extends "base.html" %}

{% block title %}Class Management - Teacher Dashboard{% endblock %}

{% block content %}
<div class="class-management-container">
    <div class="dashboard-header">
        <h1>🏫 Class Management</h1>
        <div class="header-actions">
            <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
        </div>
    </div>

    <div class="classes-overview">
        {% if class_data %}
            <div class="overview-stats">
                {% set total_students = class_data | sum(attribute='student_count') %}
                {% set total_assignments = class_data | sum(attribute='assignment_count') %}
                {% set avg_completion = (class_data | sum(attribute='completion_rate') / class_data | length) | round(1) if class_data | length > 0 else 0 %}
                
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-content">
                        <h3>{{ class_data | length }}</h3>
                        <p>Total Classes</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3>{{ total_students }}</h3>
                        <p>Total Students</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📝</div>
                    <div class="stat-content">
                        <h3>{{ total_assignments }}</h3>
                        <p>Total Assignments</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3>{{ avg_completion }}%</h3>
                        <p>Avg Completion</p>
                    </div>
                </div>
            </div>

            <div class="classes-grid">
                {% for data in class_data %}
                    {% set class = data.class %}
                    <div class="class-card">
                        <div class="class-header">
                            <h3>{{ class.subject }}</h3>
                            <div class="class-meta">
                                <span class="room-info">Room {{ class.room }}</span>
                                <span class="schedule-info">{{ class.schedule_time.strftime('%A %I:%M %p') if class.schedule_time else 'No schedule' }}</span>
                            </div>
                        </div>
                        
                        <div class="class-stats">
                            <div class="stat-row">
                                <div class="stat-item">
                                    <span class="stat-label">Students</span>
                                    <span class="stat-value">{{ data.student_count }}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Assignments</span>
                                    <span class="stat-value">{{ data.assignment_count }}</span>
                                </div>
                            </div>
                            <div class="stat-row">
                                <div class="stat-item">
                                    <span class="stat-label">Avg Grade</span>
                                    <span class="stat-value grade-{{ 'a' if data.average_grade >= 90 else 'b' if data.average_grade >= 80 else 'c' if data.average_grade >= 70 else 'd' if data.average_grade >= 60 else 'f' }}">
                                        {{ data.average_grade }}%
                                    </span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Completion</span>
                                    <span class="stat-value">{{ data.completion_rate }}%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="students-preview">
                            <h4>Students ({{ data.student_count }})</h4>
                            {% if data.students %}
                                <div class="students-list">
                                    {% for student in data.students[:5] %}
                                        <div class="student-item">
                                            <span class="student-name">{{ student.first_name }} {{ student.last_name }}</span>
                                            <span class="student-grade">Grade {{ student.grade }}</span>
                                        </div>
                                    {% endfor %}
                                    {% if data.students | length > 5 %}
                                        <div class="student-item more-students">
                                            <span>+ {{ data.students | length - 5 }} more students</span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <p class="no-students">No students enrolled</p>
                            {% endif %}
                        </div>
                        
                        <div class="class-actions">
                            <a href="{{ url_for('teacher.class_detail', class_id=class.id) }}" class="btn btn-primary btn-sm">View Details</a>
                            <a href="{{ url_for('teacher.assignments') }}?class_id={{ class.id }}" class="btn btn-outline-secondary btn-sm">Assignments</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">🏫</div>
                <h3>No Classes Assigned</h3>
                <p>You don't have any classes assigned yet. Contact your administrator to get classes assigned to you.</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
.class-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin-bottom: 2em;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    display: flex;
    align-items: center;
    gap: 1em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 2em;
    color: var(--primary);
}

.stat-content p {
    margin: 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5em;
}

.class-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
    transition: transform 0.2s ease;
}

.class-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.class-header {
    margin-bottom: 1em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid var(--border);
}

.class-header h3 {
    margin: 0 0 0.5em 0;
    color: var(--primary);
}

.class-meta {
    display: flex;
    gap: 1em;
    font-size: 0.9em;
    color: var(--secondary);
}

.room-info, .schedule-info {
    background: rgba(0,0,0,0.05);
    padding: 0.25em 0.5em;
    border-radius: 4px;
}

.class-stats {
    margin-bottom: 1em;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5em;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.8em;
    color: var(--secondary);
    margin-bottom: 0.25em;
}

.stat-value {
    font-weight: bold;
    color: var(--text);
}

.stat-value.grade-a { color: #28a745; }
.stat-value.grade-b { color: #17a2b8; }
.stat-value.grade-c { color: #ffc107; }
.stat-value.grade-d { color: #fd7e14; }
.stat-value.grade-f { color: #dc3545; }

.students-preview {
    margin-bottom: 1em;
}

.students-preview h4 {
    margin: 0 0 0.5em 0;
    color: var(--text);
    font-size: 1em;
}

.students-list {
    max-height: 150px;
    overflow-y: auto;
}

.student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.student-item:last-child {
    border-bottom: none;
}

.student-name {
    font-weight: 500;
    color: var(--text);
}

.student-grade {
    font-size: 0.85em;
    color: var(--secondary);
}

.more-students {
    font-style: italic;
    color: var(--secondary);
    justify-content: center;
}

.no-students {
    color: var(--muted);
    font-style: italic;
    text-align: center;
    margin: 1em 0;
}

.class-actions {
    display: flex;
    gap: 0.5em;
    justify-content: center;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

@media (max-width: 768px) {
    .class-management-container {
        padding: 1em;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1em;
        text-align: center;
    }
    
    .overview-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .classes-grid {
        grid-template-columns: 1fr;
    }
    
    .class-meta {
        flex-direction: column;
        gap: 0.5em;
    }
    
    .stat-row {
        flex-direction: column;
        gap: 0.5em;
    }
    
    .class-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .overview-stats {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
