{% extends "base.html" %}

{% block title %}Notification Management - Admin Dashboard{% endblock %}

{% block content %}
<div class="notification-management-container">
    <div class="dashboard-header">
        <h1>📢 Notification Management</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin.create_notification') }}" class="btn btn-primary">
                ➕ Create Notification
            </a>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                ← Back to Dashboard
            </a>
        </div>
    </div>

    <div class="notifications-container">
        {% if notifications %}
            <div class="notifications-stats">
                {% set active_count = notifications | selectattr('is_active') | list | length %}
                {% set inactive_count = notifications | length - active_count %}
                
                <div class="stat-card">
                    <div class="stat-icon">📢</div>
                    <div class="stat-content">
                        <h3>{{ notifications | length }}</h3>
                        <p>Total Notifications</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3>{{ active_count }}</h3>
                        <p>Active</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⏸️</div>
                    <div class="stat-content">
                        <h3>{{ inactive_count }}</h3>
                        <p>Inactive</p>
                    </div>
                </div>
            </div>

            <div class="notifications-list">
                {% for notification in notifications %}
                    <div class="notification-card {% if not notification.is_active %}inactive{% endif %}">
                        <div class="notification-header">
                            <div class="notification-title-section">
                                <h3>{{ notification.title }}</h3>
                                <div class="notification-meta">
                                    <span class="notification-type type-{{ notification.notification_type }}">
                                        {% if notification.notification_type == 'info' %}ℹ️ Info
                                        {% elif notification.notification_type == 'success' %}✅ Success
                                        {% elif notification.notification_type == 'warning' %}⚠️ Warning
                                        {% elif notification.notification_type == 'danger' %}🚨 Urgent
                                        {% endif %}
                                    </span>
                                    <span class="notification-audience">
                                        👥 {{ notification.recipient_role.title() if notification.recipient_role else 'All Users' }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="notification-status">
                                {% if notification.is_active %}
                                    <span class="status-badge active">Active</span>
                                {% else %}
                                    <span class="status-badge inactive">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="notification-content">
                            <p>{{ notification.message }}</p>
                        </div>
                        
                        <div class="notification-details">
                            <div class="detail-item">
                                <strong>Created:</strong> {{ notification.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </div>
                            <div class="detail-item">
                                <strong>By:</strong> {{ notification.sender.username }}
                            </div>
                            {% if notification.expires_at %}
                                <div class="detail-item">
                                    <strong>Expires:</strong> 
                                    <span class="{% if notification.expires_at < now() %}expired{% endif %}">
                                        {{ notification.expires_at.strftime('%B %d, %Y at %I:%M %p') }}
                                    </span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="notification-actions">
                            <a href="{{ url_for('admin.toggle_notification', notification_id=notification.id) }}" 
                               class="btn btn-sm {% if notification.is_active %}btn-warning{% else %}btn-success{% endif %}">
                                {% if notification.is_active %}
                                    ⏸️ Deactivate
                                {% else %}
                                    ▶️ Activate
                                {% endif %}
                            </a>
                            
                            <form method="POST" action="{{ url_for('admin.delete_notification', notification_id=notification.id) }}" 
                                  style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this notification?')">
                                <button type="submit" class="btn btn-sm btn-danger">
                                    🗑️ Delete
                                </button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📢</div>
                <h3>No Notifications</h3>
                <p>You haven't created any notifications yet. Create your first notification to communicate with users.</p>
                <a href="{{ url_for('admin.create_notification') }}" class="btn btn-primary">Create First Notification</a>
            </div>
        {% endif %}
    </div>
</div>

<style>
.notification-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1em;
}

.notifications-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin-bottom: 2em;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    display: flex;
    align-items: center;
    gap: 1em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 2em;
    color: var(--primary);
}

.stat-content p {
    margin: 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.notifications-list {
    display: grid;
    gap: 1.5em;
}

.notification-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
    transition: opacity 0.3s ease;
}

.notification-card.inactive {
    opacity: 0.6;
    background: rgba(0,0,0,0.02);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1em;
}

.notification-title-section h3 {
    margin: 0 0 0.5em 0;
    color: var(--primary);
}

.notification-meta {
    display: flex;
    gap: 1em;
    flex-wrap: wrap;
}

.notification-type {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.type-info { background: #d1ecf1; color: #0c5460; }
.type-success { background: #d4edda; color: #155724; }
.type-warning { background: #fff3cd; color: #856404; }
.type-danger { background: #f8d7da; color: #721c24; }

.notification-audience {
    background: rgba(0,0,0,0.05);
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    color: var(--secondary);
}

.status-badge {
    padding: 0.5em 1em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.notification-content {
    margin-bottom: 1em;
    padding: 1em;
    background: rgba(0,0,0,0.02);
    border-radius: 6px;
    border-left: 4px solid var(--primary);
}

.notification-content p {
    margin: 0;
    line-height: 1.5;
    color: var(--text);
}

.notification-details {
    display: flex;
    gap: 2em;
    margin-bottom: 1em;
    flex-wrap: wrap;
}

.detail-item {
    font-size: 0.9em;
    color: var(--secondary);
}

.detail-item strong {
    color: var(--text);
}

.expired {
    color: #dc3545;
    font-weight: bold;
}

.notification-actions {
    display: flex;
    gap: 0.5em;
    justify-content: flex-end;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

@media (max-width: 768px) {
    .notification-management-container {
        padding: 1em;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1em;
        text-align: center;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .notifications-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .notification-header {
        flex-direction: column;
        gap: 1em;
    }
    
    .notification-details {
        flex-direction: column;
        gap: 0.5em;
    }
    
    .notification-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .notifications-stats {
        grid-template-columns: 1fr;
    }
    
    .notification-meta {
        flex-direction: column;
        gap: 0.5em;
    }
}
</style>
{% endblock %}
