#!/usr/bin/env python3
"""
Simple test to debug login and CSRF issues.
"""

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://127.0.0.1:5000"

def test_login_page():
    """Test login page and CSRF token extraction."""
    print("🔍 Testing login page...")
    
    try:
        response = requests.get(f"{BASE_URL}/login", timeout=10)
        print(f"Login page status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for CSRF token
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            if csrf_input:
                print(f"✅ CSRF token found: {csrf_input.get('value', 'No value')[:20]}...")
            else:
                print("❌ CSRF token not found")
                
                # Debug: print form structure
                forms = soup.find_all('form')
                print(f"Found {len(forms)} forms")
                for i, form in enumerate(forms):
                    inputs = form.find_all('input')
                    print(f"Form {i}: {len(inputs)} inputs")
                    for inp in inputs:
                        print(f"  - {inp.get('name', 'no-name')}: {inp.get('type', 'no-type')}")
            
            # Look for login form
            login_form = soup.find('form')
            if login_form:
                print("✅ Login form found")
            else:
                print("❌ Login form not found")
                
        else:
            print(f"❌ Login page failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_debug_routes():
    """Test debug routes endpoint."""
    print("\n🔍 Testing debug routes...")
    
    try:
        response = requests.get(f"{BASE_URL}/debug/routes", timeout=10)
        print(f"Debug routes status: {response.status_code}")
        
        if response.status_code == 200:
            routes = response.json()
            print(f"✅ Found {len(routes)} routes")
            
            # Look for key routes
            key_routes = ['/login', '/admin/dashboard', '/teacher/dashboard']
            for route in key_routes:
                found = any(route in r for r in routes)
                print(f"  {'✅' if found else '❌'} {route}")
                
        else:
            print(f"❌ Debug routes failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_basic_pages():
    """Test basic page accessibility."""
    print("\n🔍 Testing basic pages...")
    
    pages = [
        ('/', 'Home'),
        ('/login', 'Login'),
        ('/register', 'Register'),
        ('/health', 'Health')
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{url}", timeout=10)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"  {status} {name}: {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")

if __name__ == "__main__":
    print("🧪 Simple Login and CSRF Test")
    print("=" * 40)
    
    test_basic_pages()
    test_debug_routes()
    test_login_page()
    
    print("\n" + "=" * 40)
    print("Test complete")
