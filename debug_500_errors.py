#!/usr/bin/env python3
"""
Debug script to investigate 500 errors in admin and student dashboards.
"""

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    try:
        response = session.get(url, timeout=10)
        if response.status_code != 200:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        return csrf_input['value'] if csrf_input else None
    except:
        return None

def login_as_role(role, username, password):
    """Login as a specific role and return session."""
    session = requests.Session()
    
    # Get CSRF token
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        print(f"❌ Could not get CSRF token for {role}")
        return None
    
    # Login
    login_data = {
        'username': username,
        'password': password,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    if response.status_code in [302, 303]:
        print(f"✅ {role} login successful")
        return session
    else:
        print(f"❌ {role} login failed: {response.status_code}")
        return None

def debug_dashboard_error(role, username, password):
    """Debug dashboard 500 errors."""
    print(f"\n🔍 Debugging {role} dashboard...")
    
    session = login_as_role(role, username, password)
    if not session:
        return
    
    try:
        # Try to access dashboard
        response = session.get(f"{BASE_URL}/{role}/dashboard", timeout=10)
        print(f"Dashboard response: {response.status_code}")
        
        if response.status_code == 500:
            print("❌ 500 Internal Server Error")
            print("Response content:")
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
        elif response.status_code == 200:
            print("✅ Dashboard loads successfully")
            soup = BeautifulSoup(response.content, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"Page title: {title.get_text()}")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error accessing dashboard: {e}")

def test_admin_notification_page():
    """Test admin notification page to find missing create button."""
    print(f"\n🔍 Debugging admin notification page...")
    
    session = login_as_role('admin', 'admin', 'Admin123!')
    if not session:
        return
    
    try:
        response = session.get(f"{BASE_URL}/admin/notifications", timeout=10)
        print(f"Notifications page response: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for create button
            create_links = soup.find_all('a', href=lambda x: x and 'create' in x)
            print(f"Found {len(create_links)} create links:")
            for link in create_links:
                print(f"  - {link.get('href')} : {link.get_text().strip()}")
            
            # Look for specific create notification button
            create_notification = soup.find('a', href=lambda x: x and 'create_notification' in x)
            if create_notification:
                print("✅ Create notification button found")
            else:
                print("❌ Create notification button missing")
                
                # Debug: look for any buttons
                all_buttons = soup.find_all(['a', 'button'])
                print(f"All buttons/links found ({len(all_buttons)}):")
                for btn in all_buttons[:10]:  # Show first 10
                    href = btn.get('href', 'no-href')
                    text = btn.get_text().strip()[:50]
                    print(f"  - {href} : {text}")
                    
        else:
            print(f"❌ Notifications page error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error accessing notifications page: {e}")

def main():
    """Run debug tests."""
    print("🔧 Debug 500 Errors and Missing Elements")
    print("=" * 50)
    
    # Test admin dashboard 500 error
    debug_dashboard_error('admin', 'admin', 'Admin123!')
    
    # Test student dashboard 500 error  
    debug_dashboard_error('student', 'student1', 'Student123!')
    
    # Test admin notification page
    test_admin_notification_page()
    
    print("\n" + "=" * 50)
    print("Debug complete")

if __name__ == "__main__":
    main()
