{% extends "base.html" %}

{% block title %}System Overview - Admin Dashboard{% endblock %}

{% block content %}
<div class="system-overview-container">
    <div class="dashboard-header">
        <h1>📊 System Overview</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
        </div>
    </div>

    <div class="overview-content">
        <!-- System Statistics -->
        <div class="stats-section">
            <h2>📈 System Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card users">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3>{{ stats.total_users }}</h3>
                        <p>Total Users</p>
                        <small>{{ stats.approved_users }} approved, {{ stats.pending_users }} pending</small>
                    </div>
                </div>
                
                <div class="stat-card submissions">
                    <div class="stat-icon">📄</div>
                    <div class="stat-content">
                        <h3>{{ stats.total_submissions }}</h3>
                        <p>Total Submissions</p>
                        <small>{{ stats.graded_submissions }} graded, {{ stats.pending_grading }} pending</small>
                    </div>
                </div>
                
                <div class="stat-card assignments">
                    <div class="stat-icon">📝</div>
                    <div class="stat-content">
                        <h3>{{ stats.total_assignments }}</h3>
                        <p>Total Assignments</p>
                        <small>Across {{ stats.total_classes }} classes</small>
                    </div>
                </div>
                
                <div class="stat-card grades">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3>{{ stats.avg_grade }}%</h3>
                        <p>Average Grade</p>
                        <small>System-wide performance</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Distribution -->
        <div class="grade-distribution-section">
            <h2>📊 Grade Distribution</h2>
            <div class="grade-chart">
                {% for grade, count in stats.grade_distribution.items() %}
                    <div class="grade-bar">
                        <div class="grade-label">{{ grade }}</div>
                        <div class="grade-progress">
                            {% set percentage = (count / stats.graded_submissions * 100) if stats.graded_submissions > 0 else 0 %}
                            <div class="grade-fill grade-{{ grade.lower() }}" style="width: {{ percentage }}%"></div>
                        </div>
                        <div class="grade-count">{{ count }}</div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Submissions -->
        <div class="recent-submissions-section">
            <h2>📋 Recent File Submissions</h2>
            {% if recent_submissions %}
                <div class="submissions-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Assignment</th>
                                <th>Subject</th>
                                <th>Teacher</th>
                                <th>Submitted</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for submission in recent_submissions %}
                                <tr>
                                    <td>
                                        <div class="student-info">
                                            <strong>{{ submission.student.first_name }} {{ submission.student.last_name }}</strong>
                                            <small>Grade {{ submission.student.grade }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="assignment-info">
                                            <strong>{{ submission.assignment.title }}</strong>
                                            <small>Due: {{ submission.assignment.due_date.strftime('%m/%d/%Y') }}</small>
                                        </div>
                                    </td>
                                    <td>{{ submission.assignment.class_.subject }}</td>
                                    <td>{{ submission.assignment.class_.teacher.first_name }} {{ submission.assignment.class_.teacher.last_name }}</td>
                                    <td>
                                        <div class="submission-time">
                                            {{ submission.submitted_at.strftime('%m/%d/%Y') }}
                                            <small>{{ submission.submitted_at.strftime('%I:%M %p') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if submission.grade is not none %}
                                            <span class="status-badge graded">
                                                Graded ({{ submission.grade }}%)
                                            </span>
                                        {% else %}
                                            <span class="status-badge pending">
                                                Pending Grade
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if submission.file_path %}
                                            <a href="{{ url_for('download_file', filename=submission.file_path) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Download submission">
                                                📥 Download
                                            </a>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <h3>No Submissions Yet</h3>
                    <p>No file submissions have been made to the system yet.</p>
                </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions-section">
            <h2>⚡ Quick Actions</h2>
            <div class="actions-grid">
                <a href="{{ url_for('admin.users') }}" class="action-card">
                    <div class="action-icon">👥</div>
                    <h3>Manage Users</h3>
                    <p>Approve registrations and manage user accounts</p>
                </a>
                
                <a href="{{ url_for('admin.notifications') }}" class="action-card">
                    <div class="action-icon">📢</div>
                    <h3>Send Notifications</h3>
                    <p>Create and manage system notifications</p>
                </a>
                
                <a href="{{ url_for('admin.database') }}" class="action-card">
                    <div class="action-icon">🗄️</div>
                    <h3>Database Management</h3>
                    <p>View and manage database tables</p>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.system-overview-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.overview-content {
    display: grid;
    gap: 2em;
}

.stats-section h2,
.grade-distribution-section h2,
.recent-submissions-section h2,
.quick-actions-section h2 {
    color: var(--primary);
    margin-bottom: 1em;
    font-size: 1.3em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    display: flex;
    align-items: center;
    gap: 1em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 2em;
    color: var(--primary);
}

.stat-content p {
    margin: 0.25em 0 0 0;
    color: var(--text);
    font-weight: 500;
}

.stat-content small {
    color: var(--secondary);
    font-size: 0.85em;
}

.grade-chart {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.grade-bar {
    display: grid;
    grid-template-columns: 30px 1fr 50px;
    align-items: center;
    gap: 1em;
    margin-bottom: 1em;
}

.grade-label {
    font-weight: bold;
    color: var(--text);
}

.grade-progress {
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
}

.grade-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.grade-a { background: #28a745; }
.grade-b { background: #17a2b8; }
.grade-c { background: #ffc107; }
.grade-d { background: #fd7e14; }
.grade-f { background: #dc3545; }

.grade-count {
    text-align: right;
    font-weight: bold;
    color: var(--text);
}

.submissions-table {
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1em;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.table th {
    background: rgba(0,0,0,0.02);
    font-weight: bold;
    color: var(--text);
}

.student-info,
.assignment-info {
    display: flex;
    flex-direction: column;
}

.student-info strong,
.assignment-info strong {
    color: var(--text);
}

.student-info small,
.assignment-info small {
    color: var(--secondary);
    font-size: 0.85em;
}

.submission-time {
    display: flex;
    flex-direction: column;
}

.submission-time small {
    color: var(--secondary);
    font-size: 0.85em;
}

.status-badge {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-badge.graded {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
}

.action-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    text-decoration: none;
    color: var(--text);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
    transition: transform 0.2s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
    color: var(--text);
}

.action-icon {
    font-size: 2.5em;
    margin-bottom: 0.5em;
}

.action-card h3 {
    margin: 0.5em 0;
    color: var(--primary);
}

.action-card p {
    margin: 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

@media (max-width: 768px) {
    .system-overview-container {
        padding: 1em;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1em;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table {
        font-size: 0.9em;
    }
    
    .table th,
    .table td {
        padding: 0.5em;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .grade-bar {
        grid-template-columns: 1fr;
        gap: 0.5em;
        text-align: center;
    }
    
    .table {
        font-size: 0.8em;
    }
}
</style>
{% endblock %}
