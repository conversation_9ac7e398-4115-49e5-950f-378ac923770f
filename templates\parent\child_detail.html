{% extends "base.html" %}

{% block title %}{{ child.first_name }} {{ child.last_name }} - Details{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>{{ child.first_name }} {{ child.last_name }}</h1>
        <div class="action-buttons">
            <a href="{{ url_for('parent.child_schedule', child_id=child.id) }}" class="btn btn-primary">📅 View Schedule</a>
            <a href="{{ url_for('parent.children') }}" class="btn btn-secondary">Back to Children</a>
        </div>
    </div>

    <div class="child-overview">
        <div class="card">
            <h3>Student Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <strong>Full Name:</strong> {{ child.first_name }} {{ child.last_name }}
                </div>
                <div class="info-item">
                    <strong>Grade:</strong> {{ child.grade }}
                </div>
                <div class="info-item">
                    <strong>Date of Birth:</strong> {{ child.dob.strftime('%B %d, %Y') }}
                </div>
                <div class="info-item">
                    <strong>Age:</strong> 
                    {% set age = (now().date() - child.dob).days // 365 %}
                    {{ age }} years old
                </div>
            </div>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>Enrolled Classes</h2>
        {% if enrollments %}
            <div class="classes-grid">
                {% for enrollment in enrollments %}
                    {% set class = enrollment.class_ %}
                    <div class="card class-card">
                        <div class="card-header">
                            <h4>{{ class.subject }}</h4>
                            <span class="badge badge-info">{{ class.room }}</span>
                        </div>
                        <div class="card-body">
                            <div class="class-info">
                                <div class="info-item">
                                    <strong>Teacher:</strong> {{ class.teacher.first_name }} {{ class.teacher.last_name }}
                                </div>
                                <div class="info-item">
                                    <strong>Schedule:</strong> {{ class.schedule_time.strftime('%A at %I:%M %p') }}
                                </div>
                                <div class="info-item">
                                    <strong>Room:</strong> {{ class.room }}
                                </div>
                                <div class="info-item">
                                    <strong>Assignments:</strong> {{ class.assignments.count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h3>No Classes</h3>
                <p>{{ child.first_name }} is not enrolled in any classes yet.</p>
            </div>
        {% endif %}
    </div>

    <div class="dashboard-section">
        <h2>Recent Activity</h2>
        <div class="activity-timeline">
            {% set recent_submissions = child.submissions.order_by(child.submissions.submitted_at.desc()).limit(5) %}
            {% if recent_submissions %}
                {% for submission in recent_submissions %}
                    <div class="activity-item">
                        <div class="activity-icon">📝</div>
                        <div class="activity-content">
                            <h5>Assignment Submitted</h5>
                            <p>{{ submission.assignment.title }} for {{ submission.assignment.class_.subject }}</p>
                            <small>{{ submission.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}</small>
                            {% if submission.grade %}
                                <span class="badge badge-success">Grade: {{ submission.grade }}</span>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>No Recent Activity</h3>
                    <p>No recent submissions or activities for {{ child.first_name }}.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.child-overview {
    margin-bottom: 2em;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
    margin-top: 1em;
}

.info-item {
    padding: 0.5em 0;
}

.classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5em;
    margin-top: 1em;
}

.class-card {
    border-left: 4px solid var(--info);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.card-header h4 {
    margin: 0;
    color: var(--primary);
}

.class-info {
    margin: 1em 0;
}

.activity-timeline {
    margin-top: 1em;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1em;
    margin-bottom: 1em;
    background: var(--card);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.activity-icon {
    font-size: 1.5em;
    margin-right: 1em;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h5 {
    margin: 0 0 0.5em 0;
    color: var(--primary);
}

.activity-content p {
    margin: 0 0 0.5em 0;
}

.activity-content small {
    color: var(--secondary);
}

.empty-state {
    text-align: center;
    padding: 2em 1em;
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .classes-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
