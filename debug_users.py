#!/usr/bin/env python3
"""
Debug script to check current users and their approval status.
"""

from app import create_app
from models import db, User, Student, Teacher, Parent

def debug_users():
    """Check current users and their approval status."""
    app = create_app()
    
    with app.app_context():
        print("🔍 DEBUGGING USER STATUS")
        print("=" * 50)
        
        # Check all users
        users = User.query.all()
        print(f"📊 Total Users: {len(users)}")
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   Role: {user.role}")
            print(f"   Approved: {user.is_approved}")
            print(f"   Active: {user.is_active}")
        
        print("\n" + "=" * 50)
        
        # Check approved teachers
        approved_teachers = Teacher.query.join(User).filter(User.is_approved == True).all()
        print(f"👨‍🏫 Approved Teachers: {len(approved_teachers)}")
        for teacher in approved_teachers:
            print(f"   - {teacher.first_name} {teacher.last_name} ({teacher.subjects})")
        
        # Check approved students
        approved_students = Student.query.join(User).filter(User.is_approved == True).all()
        print(f"👨‍🎓 Approved Students: {len(approved_students)}")
        for student in approved_students:
            print(f"   - {student.first_name} {student.last_name} (Grade {student.grade})")
        
        # Check approved parents
        approved_parents = Parent.query.join(User).filter(User.is_approved == True).all()
        print(f"👨‍👩‍👧‍👦 Approved Parents: {len(approved_parents)}")
        for parent in approved_parents:
            print(f"   - {parent.first_name} {parent.last_name}")
        
        print("\n" + "=" * 50)
        
        # Check if there are any unapproved users
        unapproved_users = User.query.filter(User.is_approved == False).all()
        print(f"⏳ Unapproved Users: {len(unapproved_users)}")
        for user in unapproved_users:
            print(f"   - {user.username} ({user.role})")
        
        print("\n🎯 RECOMMENDATIONS:")
        if len(approved_teachers) == 0:
            print("❌ No approved teachers found!")
            print("   → Login as admin and approve teachers in /admin/pending-approvals")
        
        if len(approved_students) == 0:
            print("❌ No approved students found!")
            print("   → Login as admin and approve students in /admin/pending-approvals")
        
        if len(unapproved_users) > 0:
            print(f"⚠️ {len(unapproved_users)} users need approval")
            print("   → Go to /admin/pending-approvals to approve them")

if __name__ == "__main__":
    debug_users()
