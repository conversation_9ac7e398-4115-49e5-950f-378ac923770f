<!DOCTYPE html>
<html>
<head>
    <title>{{ child.first_name }}'s Schedule - Parent Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3498db;
            margin-bottom: 0.5em;
        }
        .child-info {
            background: #f8f9fa;
            padding: 1em;
            border-radius: 6px;
            margin-bottom: 2em;
            border-left: 4px solid #3498db;
        }
        .schedule-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1em;
            margin-bottom: 2em;
        }
        .day-column {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        .day-header {
            background: #3498db;
            color: white;
            padding: 1em;
            text-align: center;
        }
        .day-header h3 {
            margin: 0;
            font-size: 1.1em;
        }
        .day-classes {
            padding: 1em;
            min-height: 300px;
        }
        .class-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.75em;
            margin-bottom: 0.5em;
            transition: all 0.2s ease;
        }
        .class-card:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .class-time {
            font-weight: bold;
            color: #3498db;
            font-size: 0.9em;
        }
        .class-subject {
            font-weight: bold;
            margin: 0.25em 0;
            font-size: 1.1em;
        }
        .class-teacher {
            color: #666;
            font-size: 0.9em;
        }
        .class-room {
            color: #666;
            font-size: 0.9em;
        }
        .no-classes {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 2em 0;
        }
        .empty-state {
            text-align: center;
            padding: 3em;
            color: #666;
        }
        .empty-icon {
            font-size: 4em;
            margin-bottom: 0.5em;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .navigation {
            margin-top: 2em;
            text-align: center;
        }
        @media (max-width: 768px) {
            .schedule-grid {
                grid-template-columns: 1fr;
            }
            .container {
                padding: 10px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 {{ child.first_name }}'s Class Schedule</h1>
        
        <div class="child-info">
            <h3>Student Information</h3>
            <p><strong>Name:</strong> {{ child.first_name }} {{ child.last_name }}</p>
            <p><strong>Grade:</strong> {{ child.grade }}</p>
            <p><strong>Date of Birth:</strong> {{ child.dob.strftime('%B %d, %Y') }}</p>
        </div>

        {% if schedule %}
            <div class="schedule-grid">
                {% set days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] %}
                {% for day in days %}
                    <div class="day-column">
                        <div class="day-header">
                            <h3>{{ day }}</h3>
                        </div>
                        <div class="day-classes">
                            {% if schedule.get(day) %}
                                {% for class in schedule[day] %}
                                    <div class="class-card">
                                        <div class="class-time">🕐 {{ class.time }}</div>
                                        <div class="class-subject">{{ class.subject }}</div>
                                        <div class="class-teacher">👨‍🏫 {{ class.teacher }}</div>
                                        <div class="class-room">🏫 Room {{ class.room }}</div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="no-classes">
                                    <p>No classes scheduled</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h3>No Classes Enrolled</h3>
                <p>{{ child.first_name }} is not currently enrolled in any classes.</p>
            </div>
        {% endif %}

        <div class="navigation">
            <a href="{{ url_for('parent.child_detail', child_id=child.id) }}" class="btn btn-secondary">
                ← Back to {{ child.first_name }}'s Details
            </a>
            <a href="{{ url_for('parent.children') }}" class="btn">
                👨‍👩‍👧‍👦 All Children
            </a>
            <a href="{{ url_for('parent.dashboard') }}" class="btn">
                🏠 Parent Dashboard
            </a>
        </div>
    </div>
</body>
</html>
