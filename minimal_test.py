#!/usr/bin/env python3
"""
Minimal Flask app to test basic functionality.
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <h1>🎉 Flask School Management System</h1>
    <h2>✅ Server is running successfully!</h2>
    <p>The Flask server is working. Now testing the full application...</p>
    <ul>
        <li><a href="/test">Test Route</a></li>
        <li><a href="/health">Health Check</a></li>
    </ul>
    '''

@app.route('/test')
def test():
    return '<h2>✅ Test route working!</h2><a href="/">← Back to home</a>'

@app.route('/health')
def health():
    return {'status': 'ok', 'message': 'Server is running'}

if __name__ == '__main__':
    print("🚀 Starting minimal Flask test server...")
    print("🌐 Open: http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
