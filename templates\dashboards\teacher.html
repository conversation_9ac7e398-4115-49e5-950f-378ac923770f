{% extends "base.html" %}

{% block title %}Teacher Dashboard{% endblock %}

{% block extra_css %}
<style>
/* Notification Styles */
.notifications-list {
    margin-top: 1em;
}

.notification-item {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1em;
    margin-bottom: 0.5em;
    border-radius: 4px;
}

.notification-item.notification-info {
    border-left-color: #17a2b8;
}

.notification-item.notification-success {
    border-left-color: #28a745;
}

.notification-item.notification-warning {
    border-left-color: #ffc107;
}

.notification-item.notification-danger {
    border-left-color: #dc3545;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5em;
}

.notification-header h4 {
    margin: 0;
    font-size: 1.1em;
}

.notification-date {
    font-size: 0.9em;
    color: #666;
}

.notification-item p {
    margin: 0;
    line-height: 1.4;
}

.more-notifications {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 1em;
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>Teacher Dashboard</h1>
    <p class="dashboard-welcome">Welcome, {{ teacher.first_name }} {{ teacher.last_name }}!</p>

    <div class="dashboard-stats">
        <div class="card">
            <h3>Classes</h3>
            <p class="stat">{{ classes_count }}</p>
        </div>
        <div class="card">
            <h3>Students</h3>
            <p class="stat">{{ students_count }}</p>
        </div>
        <div class="card">
            <h3>Assignments</h3>
            <p class="stat">{{ assignments_count }}</p>
        </div>
        <div class="card">
            <h3>Pending Submissions</h3>
            <p class="stat">{{ pending_submissions }}</p>
        </div>
    </div>

    <!-- Notifications Section -->
    {% if notifications %}
    <div class="dashboard-section">
        <h2>📢 Notifications</h2>
        <div class="notifications-list">
            {% for notification in notifications[:3] %}
                <div class="notification-item notification-{{ notification.notification_type }}">
                    <div class="notification-header">
                        <h4>{{ notification.title }}</h4>
                        <span class="notification-date">{{ notification.created_at.strftime('%b %d, %Y') }}</span>
                    </div>
                    <p>{{ notification.message }}</p>
                </div>
            {% endfor %}
            {% if notifications|length > 3 %}
                <p class="more-notifications">... and {{ notifications|length - 3 }} more notifications</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <div class="dashboard-section">
        <h2>Your Classes</h2>
        {% if classes %}
            <div class="class-list">
                {% for class in classes %}
                    <div class="class-item">
                        <h3>{{ class.subject }}</h3>
                        <p><strong>Room:</strong> {{ class.room }}</p>
                        <p><strong>Schedule:</strong> {{ class.schedule_time.strftime('%A, %H:%M') }}</p>
                        <a href="{{ url_for('teacher.class_detail', class_id=class.id) }}" class="btn btn-sm">View Details</a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You don't have any classes yet. <a href="{{ url_for('teacher.create_class') }}">Create a class</a>.</p>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.manage_classes') }}" class="btn btn-primary">🏫 Class Management</a>
            <a href="{{ url_for('teacher.assignments') }}" class="btn btn-info">📝 Assignment Center</a>
            <a href="{{ url_for('teacher.classes') }}" class="btn">📚 View Classes</a>
            <a href="{{ url_for('teacher.create_class') }}" class="btn">➕ Create Class</a>
            <a href="{{ url_for('teacher.profile') }}" class="btn">👤 Edit Profile</a>
        </div>
    </div>
</div>
{% endblock %}