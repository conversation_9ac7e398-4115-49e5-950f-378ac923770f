from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from functools import wraps
from models import db, User, Student, Teacher, Parent, Class, Assignment, Submission, Notification, ClassEnrollment
import sqlite3
import os
from flask_wtf import <PERSON>laskForm
from wtforms import StringField, SubmitField, SelectField, BooleanField, TextAreaField, DateTimeField, SelectMultipleField
from wtforms.validators import DataRequired, Email, Length, Optional
from wtforms.widgets import DateTimeLocalInput

# Custom optional datetime field that handles empty values
class OptionalDateTimeField(DateTimeField):
    """DateTimeField that properly handles empty/None values."""

    def process_formdata(self, valuelist):
        if valuelist:
            date_str = valuelist[0].strip()
            if not date_str:  # Empty string
                self.data = None
                return
        super().process_formdata(valuelist)

# Create the admin blueprint with explicit name
admin = Blueprint('admin', __name__)

# Admin access decorator
def admin_required(f):
    """Decorator to require admin role for a route."""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'admin':
            flash('Access denied. Admin privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# User edit form
class UserEditForm(FlaskForm):
    """Form for editing user information."""
    username = StringField('Username', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    role = SelectField('Role', choices=[
        ('admin', 'Admin'),
        ('teacher', 'Teacher'),
        ('student', 'Student'),
        ('parent', 'Parent')
    ])
    submit = SubmitField('Update User')

# Notification creation form
class NotificationForm(FlaskForm):
    """Form for creating system notifications."""
    title = StringField('Title', validators=[DataRequired(), Length(max=128)])
    message = TextAreaField('Message', validators=[DataRequired()],
                           render_kw={"placeholder": "Enter your notification message..."})
    notification_type = SelectField('Type', choices=[
        ('info', 'Information'),
        ('success', 'Success'),
        ('warning', 'Warning'),
        ('danger', 'Important/Urgent')
    ], validators=[DataRequired()])
    recipient_role = SelectField('Target Audience', choices=[
        ('all', 'All Users'),
        ('student', 'Students Only'),
        ('teacher', 'Teachers Only'),
        ('parent', 'Parents Only'),
        ('admin', 'Administrators Only')
    ], validators=[DataRequired()])
    expires_at = OptionalDateTimeField('Expiration Date (Optional)',
                                      format='%Y-%m-%dT%H:%M',
                                      validators=[Optional()],
                                      render_kw={"placeholder": "Leave empty for permanent notification"})
    submit = SubmitField('Create Notification')

# Admin class creation form
class AdminClassForm(FlaskForm):
    """Form for admin to create/edit classes with teacher and student selection."""
    subject = StringField('Subject', validators=[DataRequired(), Length(max=64)])
    teacher_id = SelectField('Teacher', coerce=int, validators=[DataRequired()])
    schedule_time = DateTimeField('Schedule Time', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    room = StringField('Room', validators=[DataRequired(), Length(max=20)])
    students = SelectMultipleField('Students (Optional)', coerce=int)
    submit = SubmitField('Create Class')

# Admin dashboard route
@admin.route('/dashboard')
@admin_required
def dashboard():
    """Admin dashboard page."""
    # Get counts for dashboard stats
    students_count = Student.query.count()
    teachers_count = Teacher.query.count()
    classes_count = Class.query.count()
    parents_count = Parent.query.count()

    # Get pending approval users
    pending_users = User.query.filter_by(is_approved=False).count()

    return render_template('dashboards/admin.html',
                          students_count=students_count,
                          teachers_count=teachers_count,
                          classes_count=classes_count,
                          parents_count=parents_count,
                          pending_users=pending_users)

# User management route
@admin.route('/users')
@admin_required
def users():
    """User management page."""
    # Get search parameters
    search = request.args.get('search', '')
    role = request.args.get('role', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # Query users
    query = User.query

    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.email.contains(search))
        )

    if role:
        query = query.filter_by(role=role)

    # Paginate results
    users = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('users/list.html',
                          users=users.items,
                          current_page=page,
                          total_pages=users.pages,
                          search=search,
                          role=role)

# User edit route
@admin.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(user_id):
    """Edit user information."""
    user = User.query.get_or_404(user_id)
    form = UserEditForm(obj=user)

    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.role = form.role.data
        db.session.commit()
        flash(f'User {user.username} has been updated.', 'success')
        return redirect(url_for('admin.users'))

    return render_template('users/edit.html', form=form, user=user)

# Pending approvals route
@admin.route('/pending-approvals')
@admin_required
def pending_approvals():
    """Pending user approvals page."""
    pending_users = User.query.filter_by(is_approved=False).all()
    return render_template('users/pending_approvals.html', users=pending_users)

# Approve user route
@admin.route('/approve-user/<int:user_id>', methods=['POST'])
@admin_required
def approve_user(user_id):
    """Approve a pending user."""
    user = User.query.get_or_404(user_id)
    user.is_approved = True
    db.session.commit()
    flash(f'User {user.username} has been approved.', 'success')
    return redirect(url_for('admin.pending_approvals'))

# Reject user route
@admin.route('/reject-user/<int:user_id>', methods=['POST'])
@admin_required
def reject_user(user_id):
    """Reject and delete a pending user."""
    user = User.query.get_or_404(user_id)
    db.session.delete(user)
    db.session.commit()
    flash(f'User {user.username} has been rejected and deleted.', 'success')
    return redirect(url_for('admin.pending_approvals'))

# Database view route - Admin only
@admin.route('/database')
@admin_required
def database():
    """
    Admin-only route to view all tables and their data in the SQLite database.
    This provides a comprehensive view of the database for debugging and monitoring.
    """
    # Get the database path from the Flask app config
    db_path = os.path.join(current_app.instance_path, 'database.db')

    # Connect to the SQLite database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    cursor = conn.cursor()

    # Get all table names from the SQLite database
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    table_names = [table['name'] for table in tables]

    # Exclude SQLite internal tables
    table_names = [name for name in table_names if not name.startswith('sqlite_')]

    # Dictionary to store table data
    tables = {}

    # For each table, get column names and all rows
    for table_name in table_names:
        # Get column names
        cursor.execute(f"PRAGMA table_info({table_name});")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]

        # Get all rows
        cursor.execute(f"SELECT * FROM {table_name};")
        rows = cursor.fetchall()

        # Convert rows to dictionaries for easier template rendering
        table_data = []
        for row in rows:
            row_dict = {}
            for i, column in enumerate(column_names):
                row_dict[column] = row[i]
            table_data.append(row_dict)

        # Store table info in the tables dictionary
        tables[table_name] = {
            'columns': column_names,
            'rows': table_data
        }

    # Close the database connection
    conn.close()

    return render_template('admin_database.html', tables=tables)

# Notification management routes
@admin.route('/notifications')
@admin_required
def notifications():
    """Admin notification management page."""
    notifications = Notification.query.order_by(Notification.created_at.desc()).all()
    return render_template('admin/notifications.html', notifications=notifications)

@admin.route('/notifications/create', methods=['GET', 'POST'])
@admin_required
def create_notification():
    """Create a new system notification."""
    form = NotificationForm()

    if form.validate_on_submit():
        notification = Notification(
            title=form.title.data,
            message=form.message.data,
            notification_type=form.notification_type.data,
            sender_id=current_user.id,
            recipient_role=form.recipient_role.data if form.recipient_role.data != 'all' else None,
            expires_at=form.expires_at.data
        )

        db.session.add(notification)
        db.session.commit()

        flash(f'Notification "{notification.title}" created successfully!', 'success')
        return redirect(url_for('admin.notifications'))

    return render_template('admin/create_notification.html', form=form)

@admin.route('/notifications/<int:notification_id>/toggle')
@admin_required
def toggle_notification(notification_id):
    """Toggle notification active status."""
    notification = Notification.query.get_or_404(notification_id)
    notification.is_active = not notification.is_active
    db.session.commit()

    status = "activated" if notification.is_active else "deactivated"
    flash(f'Notification "{notification.title}" {status}.', 'success')
    return redirect(url_for('admin.notifications'))

@admin.route('/notifications/<int:notification_id>/delete', methods=['POST'])
@admin_required
def delete_notification(notification_id):
    """Delete a notification."""
    notification = Notification.query.get_or_404(notification_id)
    title = notification.title

    db.session.delete(notification)
    db.session.commit()

    flash(f'Notification "{title}" deleted successfully.', 'success')
    return redirect(url_for('admin.notifications'))

# System overview route
@admin.route('/overview')
@admin_required
def system_overview():
    """Enhanced system overview with file submissions and statistics."""
    # Get comprehensive system statistics
    total_users = User.query.count()
    approved_users = User.query.filter_by(is_approved=True).count()
    pending_users = User.query.filter_by(is_approved=False).count()

    total_submissions = Submission.query.count()
    graded_submissions = Submission.query.filter(Submission.grade.isnot(None)).count()
    pending_grading = total_submissions - graded_submissions

    total_assignments = Assignment.query.count()
    total_classes = Class.query.count()

    # Get recent submissions across the system
    recent_submissions = Submission.query.join(Student).join(Assignment).join(Class).order_by(
        Submission.submitted_at.desc()
    ).limit(10).all()

    # Get system-wide grade statistics
    if graded_submissions > 0:
        grades = [s.grade for s in Submission.query.filter(Submission.grade.isnot(None)).all()]
        avg_grade = sum(grades) / len(grades)
        grade_distribution = {
            'A': len([g for g in grades if g >= 90]),
            'B': len([g for g in grades if 80 <= g < 90]),
            'C': len([g for g in grades if 70 <= g < 80]),
            'D': len([g for g in grades if 60 <= g < 70]),
            'F': len([g for g in grades if g < 60])
        }
    else:
        avg_grade = 0
        grade_distribution = {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}

    stats = {
        'total_users': total_users,
        'approved_users': approved_users,
        'pending_users': pending_users,
        'total_submissions': total_submissions,
        'graded_submissions': graded_submissions,
        'pending_grading': pending_grading,
        'total_assignments': total_assignments,
        'total_classes': total_classes,
        'avg_grade': round(avg_grade, 1),
        'grade_distribution': grade_distribution
    }

    return render_template('admin/system_overview.html',
                         stats=stats,
                         recent_submissions=recent_submissions)

# Admin class schedule management
@admin.route('/schedules')
@admin_required
def schedules():
    """Admin class schedule management page."""
    # Get all classes with their schedules
    classes = Class.query.join(Teacher).all()

    # Organize classes by day of week
    schedule_data = {}
    days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

    for day in days:
        schedule_data[day] = []

    for class_obj in classes:
        day_name = class_obj.schedule_time.strftime('%A')
        if day_name in schedule_data:
            schedule_data[day_name].append({
                'id': class_obj.id,
                'time': class_obj.schedule_time.strftime('%H:%M'),
                'subject': class_obj.subject,
                'teacher': f"{class_obj.teacher.first_name} {class_obj.teacher.last_name}",
                'room': class_obj.room,
                'student_count': class_obj.enrollments.count()
            })

    # Sort classes by time for each day
    for day in schedule_data:
        schedule_data[day].sort(key=lambda x: x['time'])

    # Calculate statistics
    total_scheduled_classes = sum(len(schedule_data[day]) for day in schedule_data)
    avg_classes_per_day = round(total_scheduled_classes / 5, 1) if total_scheduled_classes > 0 else 0

    return render_template('admin/schedules.html',
                          schedule=schedule_data,
                          total_classes=len(classes),
                          total_scheduled_classes=total_scheduled_classes,
                          avg_classes_per_day=avg_classes_per_day)

# Admin class creation route
@admin.route('/classes/create')
@admin_required
def create_class():
    """Admin class creation page."""
    form = AdminClassForm()

    # Populate teacher choices
    teachers = Teacher.query.all()
    form.teacher_id.choices = [(t.id, f"{t.first_name} {t.last_name} - {t.subjects}") for t in teachers]

    # Populate student choices
    students = Student.query.all()
    form.students.choices = [(s.id, f"{s.first_name} {s.last_name} (Grade {s.grade})") for s in students]

    if form.validate_on_submit():
        # Create new class
        new_class = Class(
            teacher_id=form.teacher_id.data,
            subject=form.subject.data,
            schedule_time=form.schedule_time.data,
            room=form.room.data
        )

        db.session.add(new_class)
        db.session.flush()  # Get the class ID

        # Add selected students to the class
        if form.students.data:
            for student_id in form.students.data:
                enrollment = ClassEnrollment(
                    student_id=student_id,
                    class_id=new_class.id
                )
                db.session.add(enrollment)

        db.session.commit()

        flash(f'Class "{new_class.subject}" created successfully!', 'success')
        return redirect(url_for('admin.schedules'))

    return render_template('admin/create_class.html', form=form, title='Create New Class')

# Admin class editing route
@admin.route('/classes/<int:class_id>/edit')
@admin_required
def edit_class(class_id):
    """Admin class editing page."""
    class_obj = Class.query.get_or_404(class_id)
    form = AdminClassForm(obj=class_obj)

    # Populate teacher choices
    teachers = Teacher.query.all()
    form.teacher_id.choices = [(t.id, f"{t.first_name} {t.last_name} - {t.subjects}") for t in teachers]

    # Populate student choices
    students = Student.query.all()
    form.students.choices = [(s.id, f"{s.first_name} {s.last_name} (Grade {s.grade})") for s in students]

    # Set current enrolled students
    enrolled_students = [e.student_id for e in class_obj.enrollments]
    form.students.data = enrolled_students

    if form.validate_on_submit():
        # Update class information
        class_obj.teacher_id = form.teacher_id.data
        class_obj.subject = form.subject.data
        class_obj.schedule_time = form.schedule_time.data
        class_obj.room = form.room.data

        # Update student enrollments
        # Remove all current enrollments
        ClassEnrollment.query.filter_by(class_id=class_id).delete()

        # Add new enrollments
        if form.students.data:
            for student_id in form.students.data:
                enrollment = ClassEnrollment(
                    student_id=student_id,
                    class_id=class_id
                )
                db.session.add(enrollment)

        db.session.commit()

        flash(f'Class "{class_obj.subject}" updated successfully!', 'success')
        return redirect(url_for('admin.schedules'))

    return render_template('admin/create_class.html', form=form, title='Edit Class', class_obj=class_obj)

# Admin class deletion route
@admin.route('/classes/<int:class_id>/delete')
@admin_required
def delete_class(class_id):
    """Admin class deletion."""
    class_obj = Class.query.get_or_404(class_id)
    class_name = class_obj.subject

    db.session.delete(class_obj)
    db.session.commit()

    flash(f'Class "{class_name}" deleted successfully!', 'success')
    return redirect(url_for('admin.schedules'))
