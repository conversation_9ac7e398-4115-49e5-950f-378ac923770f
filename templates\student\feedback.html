{% extends "base.html" %}

{% block title %}Performance Feedback - Student Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>💬 Performance Feedback</h1>
        <div class="student-info">
            <p><strong>Student:</strong> {{ student.first_name }} {{ student.last_name }}</p>
            <p><strong>Grade:</strong> {{ student.grade }}</p>
        </div>
    </div>

    <div class="feedback-container">
        {% if feedback %}
            <div class="feedback-grid">
                {% for subject, data in feedback.items() %}
                    <div class="subject-feedback-card">
                        <div class="subject-header">
                            <h3>{{ subject }}</h3>
                            <p class="teacher-name">{{ data.teacher }}</p>
                        </div>
                        
                        <div class="feedback-list">
                            {% for item in data.feedback_items %}
                                <div class="feedback-item">
                                    <div class="feedback-header">
                                        <div class="assignment-info">
                                            <span class="assignment-title">{{ item.assignment }}</span>
                                            <span class="assignment-date">{{ item.submitted_at.strftime('%m/%d/%Y') }}</span>
                                        </div>
                                        {% if item.grade %}
                                            {% set grade_class = 'grade-a' if item.grade >= 90 else 'grade-b' if item.grade >= 80 else 'grade-c' if item.grade >= 70 else 'grade-d' if item.grade >= 60 else 'grade-f' %}
                                            <span class="grade {{ grade_class }}">{{ item.grade }}%</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="feedback-content">
                                        <div class="feedback-text">
                                            {{ item.feedback }}
                                        </div>
                                        
                                        <!-- Parse feedback for strengths and improvements -->
                                        {% set feedback_lower = item.feedback.lower() %}
                                        {% if 'excellent' in feedback_lower or 'great' in feedback_lower or 'good' in feedback_lower or 'well done' in feedback_lower %}
                                            <div class="feedback-tag positive">
                                                <span class="tag-icon">✅</span>
                                                <span>Positive Feedback</span>
                                            </div>
                                        {% endif %}
                                        
                                        {% if 'improve' in feedback_lower or 'work on' in feedback_lower or 'needs' in feedback_lower or 'focus on' in feedback_lower %}
                                            <div class="feedback-tag improvement">
                                                <span class="tag-icon">📈</span>
                                                <span>Areas for Improvement</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Subject Summary -->
                        <div class="subject-summary">
                            <h4>📊 Subject Overview</h4>
                            {% set total_feedback = data.feedback_items | length %}
                            {% set positive_count = data.feedback_items | selectattr('feedback') | select('match', '.*(?i)(excellent|great|good|well done).*') | list | length %}
                            {% set improvement_count = data.feedback_items | selectattr('feedback') | select('match', '.*(?i)(improve|work on|needs|focus on).*') | list | length %}
                            
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <span class="stat-number">{{ total_feedback }}</span>
                                    <span class="stat-label">Total Feedback</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number positive">{{ positive_count }}</span>
                                    <span class="stat-label">Positive Notes</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number improvement">{{ improvement_count }}</span>
                                    <span class="stat-label">Growth Areas</span>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Overall Feedback Summary -->
            <div class="overall-summary">
                <h3>🎯 Overall Performance Summary</h3>
                {% set all_feedback = [] %}
                {% for subject, data in feedback.items() %}
                    {% for item in data.feedback_items %}
                        {% set _ = all_feedback.append(item) %}
                    {% endfor %}
                {% endfor %}
                
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="summary-icon">📚</div>
                        <div class="summary-content">
                            <h4>Total Assignments with Feedback</h4>
                            <p class="summary-number">{{ all_feedback | length }}</p>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">📖</div>
                        <div class="summary-content">
                            <h4>Subjects with Feedback</h4>
                            <p class="summary-number">{{ feedback.keys() | length }}</p>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">⭐</div>
                        <div class="summary-content">
                            <h4>Latest Feedback</h4>
                            {% set latest = all_feedback | sort(attribute='submitted_at', reverse=true) | first %}
                            {% if latest %}
                                <p class="summary-text">{{ latest.submitted_at.strftime('%m/%d/%Y') }}</p>
                            {% else %}
                                <p class="summary-text">No feedback yet</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">💭</div>
                <h3>No Feedback Available</h3>
                <p>You don't have any teacher feedback yet. Complete and submit assignments to receive detailed feedback from your teachers.</p>
            </div>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
        <a href="{{ url_for('student.assignments') }}" class="btn btn-primary">View Assignments</a>
        <a href="{{ url_for('student.grades') }}" class="btn btn-info">View Grades</a>
    </div>
</div>

<style>
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.student-info {
    text-align: right;
    color: var(--secondary);
}

.student-info p {
    margin: 0.25em 0;
}

.feedback-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2em;
    margin-bottom: 2em;
}

.subject-feedback-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.subject-header {
    margin-bottom: 1.5em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid var(--border);
}

.subject-header h3 {
    margin: 0 0 0.25em 0;
    color: var(--primary);
}

.teacher-name {
    margin: 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.feedback-item {
    margin-bottom: 1.5em;
    padding: 1em;
    background: rgba(0,0,0,0.02);
    border-radius: 6px;
    border-left: 4px solid var(--primary);
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75em;
}

.assignment-title {
    display: block;
    font-weight: 500;
    color: var(--text);
}

.assignment-date {
    display: block;
    font-size: 0.85em;
    color: var(--muted);
    margin-top: 0.25em;
}

.grade {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
}

.grade-a { background: #d4edda; color: #155724; }
.grade-b { background: #d1ecf1; color: #0c5460; }
.grade-c { background: #fff3cd; color: #856404; }
.grade-d { background: #f8d7da; color: #721c24; }
.grade-f { background: #f5c6cb; color: #721c24; }

.feedback-content {
    margin-top: 0.75em;
}

.feedback-text {
    color: var(--text);
    line-height: 1.5;
    margin-bottom: 0.75em;
}

.feedback-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25em;
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    margin-right: 0.5em;
}

.feedback-tag.positive {
    background: #d4edda;
    color: #155724;
}

.feedback-tag.improvement {
    background: #fff3cd;
    color: #856404;
}

.subject-summary {
    margin-top: 1.5em;
    padding-top: 1em;
    border-top: 2px solid var(--border);
}

.subject-summary h4 {
    margin: 0 0 1em 0;
    color: var(--primary);
}

.summary-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--primary);
}

.stat-number.positive {
    color: #28a745;
}

.stat-number.improvement {
    color: #ffc107;
}

.stat-label {
    font-size: 0.85em;
    color: var(--secondary);
}

.overall-summary {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 2em;
    margin-bottom: 2em;
}

.overall-summary h3 {
    margin: 0 0 1.5em 0;
    color: var(--primary);
    text-align: center;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
}

.summary-card {
    background: white;
    border-radius: 6px;
    padding: 1.5em;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-icon {
    font-size: 2em;
    margin-bottom: 0.5em;
}

.summary-content h4 {
    margin: 0 0 0.5em 0;
    color: var(--text);
    font-size: 0.9em;
}

.summary-number {
    font-size: 1.8em;
    font-weight: bold;
    color: var(--primary);
    margin: 0;
}

.summary-text {
    color: var(--secondary);
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

.dashboard-actions {
    display: flex;
    gap: 1em;
    justify-content: center;
    padding-top: 2em;
    border-top: 1px solid var(--border);
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 1em;
    }
    
    .student-info {
        text-align: center;
    }
    
    .feedback-grid {
        grid-template-columns: 1fr;
    }
    
    .feedback-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5em;
    }
    
    .summary-stats {
        flex-direction: column;
        gap: 1em;
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 1em;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
