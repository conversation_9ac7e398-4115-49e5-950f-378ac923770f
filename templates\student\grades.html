{% extends "base.html" %}

{% block title %}Final Grades - Student Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>📊 Final Grades</h1>
        <div class="student-info">
            <p><strong>Student:</strong> {{ student.first_name }} {{ student.last_name }}</p>
            <p><strong>Grade:</strong> {{ student.grade }}</p>
        </div>
    </div>

    <div class="grades-container">
        {% if grades %}
            <div class="grades-grid">
                {% for subject, data in grades.items() %}
                    <div class="subject-card">
                        <div class="subject-header">
                            <h3>{{ subject }}</h3>
                            <p class="teacher-name">{{ data.teacher }}</p>
                        </div>
                        
                        <div class="assignments-list">
                            {% set total_points = 0 %}
                            {% set earned_points = 0 %}
                            {% for assignment in data.assignments %}
                                <div class="assignment-grade">
                                    <div class="assignment-info">
                                        <span class="assignment-title">{{ assignment.title }}</span>
                                        <span class="assignment-date">{{ assignment.submitted_at.strftime('%m/%d/%Y') }}</span>
                                    </div>
                                    <div class="grade-display">
                                        {% if assignment.grade %}
                                            {% set grade_class = 'grade-a' if assignment.grade >= 90 else 'grade-b' if assignment.grade >= 80 else 'grade-c' if assignment.grade >= 70 else 'grade-d' if assignment.grade >= 60 else 'grade-f' %}
                                            <span class="grade {{ grade_class }}">{{ assignment.grade }}%</span>
                                            {% set _ = earned_points.__add__(assignment.grade) %}
                                            {% set _ = total_points.__add__(100) %}
                                        {% else %}
                                            <span class="grade grade-pending">Pending</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                            
                            {% if data.assignments %}
                                <div class="subject-average">
                                    {% set avg = (earned_points / total_points * 100) | round(1) if total_points > 0 else 0 %}
                                    {% set avg_class = 'grade-a' if avg >= 90 else 'grade-b' if avg >= 80 else 'grade-c' if avg >= 70 else 'grade-d' if avg >= 60 else 'grade-f' %}
                                    <strong>Subject Average: <span class="grade {{ avg_class }}">{{ avg }}%</span></strong>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Overall GPA Calculation -->
            <div class="gpa-card">
                <h3>📈 Overall Performance</h3>
                {% set all_grades = [] %}
                {% for subject, data in grades.items() %}
                    {% for assignment in data.assignments %}
                        {% if assignment.grade %}
                            {% set _ = all_grades.append(assignment.grade) %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}
                
                {% if all_grades %}
                    {% set overall_avg = (all_grades | sum / all_grades | length) | round(1) %}
                    {% set gpa_class = 'grade-a' if overall_avg >= 90 else 'grade-b' if overall_avg >= 80 else 'grade-c' if overall_avg >= 70 else 'grade-d' if overall_avg >= 60 else 'grade-f' %}
                    <div class="gpa-display">
                        <span class="gpa-label">Overall Average:</span>
                        <span class="gpa-value {{ gpa_class }}">{{ overall_avg }}%</span>
                    </div>
                    <div class="grade-breakdown">
                        <p><strong>Total Assignments:</strong> {{ all_grades | length }}</p>
                        <p><strong>Subjects:</strong> {{ grades.keys() | length }}</p>
                    </div>
                {% endif %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📝</div>
                <h3>No Grades Available</h3>
                <p>You don't have any graded assignments yet. Complete and submit assignments to see your grades here.</p>
            </div>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
        <a href="{{ url_for('student.assignments') }}" class="btn btn-primary">View Assignments</a>
        <a href="{{ url_for('student.feedback') }}" class="btn btn-info">View Feedback</a>
    </div>
</div>

<style>
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.student-info {
    text-align: right;
    color: var(--secondary);
}

.student-info p {
    margin: 0.25em 0;
}

.grades-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5em;
    margin-bottom: 2em;
}

.subject-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.subject-header {
    margin-bottom: 1em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid var(--border);
}

.subject-header h3 {
    margin: 0 0 0.25em 0;
    color: var(--primary);
}

.teacher-name {
    margin: 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.assignment-grade {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75em 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.assignment-grade:last-child {
    border-bottom: none;
}

.assignment-info {
    flex: 1;
}

.assignment-title {
    display: block;
    font-weight: 500;
    color: var(--text);
}

.assignment-date {
    display: block;
    font-size: 0.85em;
    color: var(--muted);
    margin-top: 0.25em;
}

.grade {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
}

.grade-a { background: #d4edda; color: #155724; }
.grade-b { background: #d1ecf1; color: #0c5460; }
.grade-c { background: #fff3cd; color: #856404; }
.grade-d { background: #f8d7da; color: #721c24; }
.grade-f { background: #f5c6cb; color: #721c24; }
.grade-pending { background: #e2e3e5; color: #6c757d; }

.subject-average {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 2px solid var(--border);
    text-align: center;
}

.gpa-card {
    background: linear-gradient(135deg, var(--primary), #0056b3);
    color: white;
    border-radius: 8px;
    padding: 2em;
    text-align: center;
    margin-bottom: 2em;
}

.gpa-card h3 {
    margin: 0 0 1em 0;
    color: white;
}

.gpa-display {
    margin-bottom: 1em;
}

.gpa-label {
    font-size: 1.2em;
    margin-right: 0.5em;
}

.gpa-value {
    font-size: 2em;
    font-weight: bold;
    padding: 0.25em 0.5em;
    border-radius: 8px;
    background: rgba(255,255,255,0.2);
}

.grade-breakdown {
    display: flex;
    justify-content: space-around;
    margin-top: 1em;
}

.grade-breakdown p {
    margin: 0;
    font-size: 0.9em;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

.dashboard-actions {
    display: flex;
    gap: 1em;
    justify-content: center;
    padding-top: 2em;
    border-top: 1px solid var(--border);
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 1em;
    }
    
    .student-info {
        text-align: center;
    }
    
    .grades-grid {
        grid-template-columns: 1fr;
    }
    
    .grade-breakdown {
        flex-direction: column;
        gap: 0.5em;
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 1em;
    }
    
    .assignment-grade {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5em;
    }
}
</style>
{% endblock %}
