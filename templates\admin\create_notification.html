{% extends "base.html" %}

{% block title %}Create Notification - Admin Dashboard{% endblock %}

{% block content %}
<div class="create-notification-container">
    <div class="form-header">
        <h1>📢 Create Notification</h1>
        <div class="breadcrumb">
            <a href="{{ url_for('admin.dashboard') }}">Dashboard</a> > 
            <a href="{{ url_for('admin.notifications') }}">Notifications</a> > 
            Create Notification
        </div>
    </div>

    <div class="form-container">
        <form method="POST" class="notification-form">
            {{ form.hidden_tag() }}
            
            <div class="form-section">
                <h3>📝 Notification Details</h3>
                
                <div class="form-group">
                    {{ form.title.label(class="form-label") }}
                    {{ form.title(class="form-control", placeholder="Enter notification title") }}
                    {% if form.title.errors %}
                        <div class="form-error">
                            {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-help">Keep it concise and descriptive</small>
                </div>

                <div class="form-group">
                    {{ form.message.label(class="form-label") }}
                    {{ form.message(class="form-control", rows="6") }}
                    {% if form.message.errors %}
                        <div class="form-error">
                            {% for error in form.message.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-help">Provide clear and helpful information</small>
                </div>
            </div>

            <div class="form-section">
                <h3>🎯 Targeting & Settings</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.notification_type.label(class="form-label") }}
                        {{ form.notification_type(class="form-control", id="notification-type") }}
                        {% if form.notification_type.errors %}
                            <div class="form-error">
                                {% for error in form.notification_type.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-help">Choose the appropriate urgency level</small>
                    </div>

                    <div class="form-group">
                        {{ form.recipient_role.label(class="form-label") }}
                        {{ form.recipient_role(class="form-control", id="recipient-role") }}
                        {% if form.recipient_role.errors %}
                            <div class="form-error">
                                {% for error in form.recipient_role.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-help">Select who should see this notification</small>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.expires_at.label(class="form-label") }}
                    {{ form.expires_at(class="form-control") }}
                    {% if form.expires_at.errors %}
                        <div class="form-error">
                            {% for error in form.expires_at.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-help">Leave empty for permanent notification</small>
                </div>
            </div>

            <div class="preview-section">
                <h3>👁️ Preview</h3>
                <div id="notification-preview" class="notification-preview">
                    <div class="preview-notification">
                        <div class="preview-header">
                            <span id="preview-type" class="preview-type type-info">ℹ️ Info</span>
                            <span id="preview-audience" class="preview-audience">👥 All Users</span>
                        </div>
                        <h4 id="preview-title">Notification Title</h4>
                        <p id="preview-message">Your notification message will appear here...</p>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <a href="{{ url_for('admin.notifications') }}" class="btn btn-secondary">Cancel</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<style>
.create-notification-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2em;
}

.form-header {
    margin-bottom: 2em;
}

.form-header h1 {
    color: var(--primary);
    margin-bottom: 0.5em;
}

.breadcrumb {
    color: var(--secondary);
    font-size: 0.9em;
}

.breadcrumb a {
    color: var(--primary);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.form-container {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 2em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.form-section {
    margin-bottom: 2em;
    padding-bottom: 1.5em;
    border-bottom: 1px solid var(--border);
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h3 {
    color: var(--primary);
    margin-bottom: 1em;
    font-size: 1.2em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1em;
}

.form-group {
    margin-bottom: 1.5em;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-control {
    width: 100%;
    padding: 0.75em;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-help {
    color: var(--secondary);
    font-size: 0.85em;
    margin-top: 0.25em;
    display: block;
}

.form-error {
    color: #dc3545;
    font-size: 0.85em;
    margin-top: 0.25em;
}

.preview-section {
    margin-bottom: 2em;
}

.preview-section h3 {
    color: var(--primary);
    margin-bottom: 1em;
}

.notification-preview {
    background: rgba(0,0,0,0.02);
    border-radius: 6px;
    padding: 1em;
    border: 1px dashed var(--border);
}

.preview-notification {
    background: white;
    border-radius: 6px;
    padding: 1em;
    border-left: 4px solid var(--primary);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-header {
    display: flex;
    gap: 1em;
    margin-bottom: 0.5em;
}

.preview-type {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.type-info { background: #d1ecf1; color: #0c5460; }
.type-success { background: #d4edda; color: #155724; }
.type-warning { background: #fff3cd; color: #856404; }
.type-danger { background: #f8d7da; color: #721c24; }

.preview-audience {
    background: rgba(0,0,0,0.05);
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    color: var(--secondary);
}

#preview-title {
    margin: 0.5em 0;
    color: var(--primary);
    font-size: 1.1em;
}

#preview-message {
    margin: 0;
    color: var(--text);
    line-height: 1.5;
}

.form-actions {
    display: flex;
    gap: 1em;
    justify-content: flex-end;
    margin-top: 2em;
    padding-top: 1.5em;
    border-top: 1px solid var(--border);
}

@media (max-width: 768px) {
    .create-notification-container {
        padding: 1em;
    }
    
    .form-container {
        padding: 1.5em;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .preview-header {
        flex-direction: column;
        gap: 0.5em;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleField = document.getElementById('title');
    const messageField = document.getElementById('message');
    const typeField = document.getElementById('notification-type');
    const audienceField = document.getElementById('recipient-role');
    
    const previewTitle = document.getElementById('preview-title');
    const previewMessage = document.getElementById('preview-message');
    const previewType = document.getElementById('preview-type');
    const previewAudience = document.getElementById('preview-audience');
    
    // Type icons and labels
    const typeConfig = {
        'info': { icon: 'ℹ️', label: 'Info' },
        'success': { icon: '✅', label: 'Success' },
        'warning': { icon: '⚠️', label: 'Warning' },
        'danger': { icon: '🚨', label: 'Urgent' }
    };
    
    // Audience labels
    const audienceConfig = {
        'all': 'All Users',
        'student': 'Students Only',
        'teacher': 'Teachers Only',
        'parent': 'Parents Only',
        'admin': 'Administrators Only'
    };
    
    function updatePreview() {
        // Update title
        previewTitle.textContent = titleField.value || 'Notification Title';
        
        // Update message
        previewMessage.textContent = messageField.value || 'Your notification message will appear here...';
        
        // Update type
        const selectedType = typeField.value;
        const typeInfo = typeConfig[selectedType];
        previewType.textContent = `${typeInfo.icon} ${typeInfo.label}`;
        previewType.className = `preview-type type-${selectedType}`;
        
        // Update audience
        const selectedAudience = audienceField.value;
        previewAudience.textContent = `👥 ${audienceConfig[selectedAudience]}`;
    }
    
    // Add event listeners
    titleField.addEventListener('input', updatePreview);
    messageField.addEventListener('input', updatePreview);
    typeField.addEventListener('change', updatePreview);
    audienceField.addEventListener('change', updatePreview);
    
    // Initial preview update
    updatePreview();
});
</script>
{% endblock %}
