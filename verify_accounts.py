#!/usr/bin/env python3
"""
Verify that all test accounts exist and have proper profiles.
"""

from app import create_app
from models import db, User, Student, Teacher, Parent

def verify_accounts():
    """Verify all test accounts exist with proper profiles."""
    app = create_app()
    
    with app.app_context():
        print("🔍 Verifying Test Accounts")
        print("=" * 50)
        
        # Check Admin accounts
        print("\n👨‍💼 ADMIN ACCOUNTS:")
        admins = User.query.filter_by(role='admin').all()
        for admin in admins:
            status = "✅ APPROVED" if admin.is_approved else "⏳ PENDING"
            print(f"  {admin.username} ({admin.email}) - {status}")
        
        # Check Teacher accounts
        print("\n👩‍🏫 TEACHER ACCOUNTS:")
        teachers = User.query.filter_by(role='teacher').all()
        for teacher_user in teachers:
            teacher_profile = Teacher.query.filter_by(user_id=teacher_user.id).first()
            status = "✅ APPROVED" if teacher_user.is_approved else "⏳ PENDING"
            profile_info = ""
            if teacher_profile:
                profile_info = f" - {teacher_profile.first_name} {teacher_profile.last_name} ({teacher_profile.subjects})"
            print(f"  {teacher_user.username} ({teacher_user.email}) - {status}{profile_info}")
        
        # Check Student accounts
        print("\n👨‍🎓 STUDENT ACCOUNTS:")
        students = User.query.filter_by(role='student').all()
        for student_user in students:
            student_profile = Student.query.filter_by(user_id=student_user.id).first()
            status = "✅ APPROVED" if student_user.is_approved else "⏳ PENDING"
            profile_info = ""
            if student_profile:
                parent_info = ""
                if student_profile.parent:
                    parent_info = f" (Parent: {student_profile.parent.first_name} {student_profile.parent.last_name})"
                profile_info = f" - {student_profile.first_name} {student_profile.last_name}, Grade {student_profile.grade}{parent_info}"
            print(f"  {student_user.username} ({student_user.email}) - {status}{profile_info}")
        
        # Check Parent accounts
        print("\n👨‍👩‍👧‍👦 PARENT ACCOUNTS:")
        parents = User.query.filter_by(role='parent').all()
        for parent_user in parents:
            parent_profile = Parent.query.filter_by(user_id=parent_user.id).first()
            status = "✅ APPROVED" if parent_user.is_approved else "⏳ PENDING"
            profile_info = ""
            if parent_profile:
                children = Student.query.filter_by(parent_id=parent_profile.id).all()
                children_names = [f"{child.first_name} {child.last_name}" for child in children]
                children_info = f" (Children: {', '.join(children_names)})" if children_names else " (No children linked)"
                profile_info = f" - {parent_profile.first_name} {parent_profile.last_name}, {parent_profile.phone}{children_info}"
            print(f"  {parent_user.username} ({parent_user.email}) - {status}{profile_info}")
        
        # Summary
        total_users = User.query.count()
        approved_users = User.query.filter_by(is_approved=True).count()
        pending_users = User.query.filter_by(is_approved=False).count()
        
        print("\n📊 SUMMARY:")
        print(f"  Total Users: {total_users}")
        print(f"  Approved: {approved_users}")
        print(f"  Pending Approval: {pending_users}")
        
        print("\n🔑 TEST CREDENTIALS:")
        print("  Admin: admin / admin123")
        print("  Teacher: teacher1 / teacher123")
        print("  Student: student1 / student123")
        print("  Parent: parent1 / parent123")
        
        print("\n🌐 ACCESS URLS:")
        print("  Home: http://127.0.0.1:5000/")
        print("  Login: http://127.0.0.1:5000/login")
        print("  Register: http://127.0.0.1:5000/register")
        print("  Admin Registration: http://127.0.0.1:5000/register/admin")
        print("  Teacher Registration: http://127.0.0.1:5000/register/teacher")
        print("  Student Registration: http://127.0.0.1:5000/register/student")
        print("  Parent Registration: http://127.0.0.1:5000/register/parent")

if __name__ == "__main__":
    verify_accounts()
