#!/usr/bin/env python3
"""
Database migration script for Phase 3 enhancements.
Updates the database schema to support enhanced grading and notifications.
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """Migrate database to Phase 3 schema."""
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print("❌ Database not found. Please run the main application first to create the database.")
        return False
    
    print("🔄 Starting Phase 3 database migration...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if migrations are needed
        print("📋 Checking current database schema...")
        
        # Check submissions table for graded_at column
        cursor.execute("PRAGMA table_info(submissions)")
        submissions_columns = [col[1] for col in cursor.fetchall()]
        
        if 'graded_at' not in submissions_columns:
            print("  ➕ Adding graded_at column to submissions table...")
            cursor.execute("ALTER TABLE submissions ADD COLUMN graded_at DATETIME")
            print("  ✅ Added graded_at column")
        else:
            print("  ✅ graded_at column already exists")
        
        # Update grade column type if needed (from STRING to INTEGER)
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='submissions'")
        table_sql = cursor.fetchone()[0]
        
        if 'grade VARCHAR(10)' in table_sql or 'grade TEXT' in table_sql:
            print("  🔄 Converting grade column from TEXT to INTEGER...")
            
            # Create new table with correct schema
            cursor.execute("""
                CREATE TABLE submissions_new (
                    id INTEGER PRIMARY KEY,
                    assignment_id INTEGER NOT NULL,
                    student_id INTEGER NOT NULL,
                    file_path VARCHAR(255) NOT NULL,
                    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    grade INTEGER,
                    feedback TEXT,
                    graded_at DATETIME,
                    FOREIGN KEY (assignment_id) REFERENCES assignments (id),
                    FOREIGN KEY (student_id) REFERENCES students (id)
                )
            """)
            
            # Copy data, converting grades to integers where possible
            cursor.execute("""
                INSERT INTO submissions_new (id, assignment_id, student_id, file_path, submitted_at, grade, feedback, graded_at)
                SELECT id, assignment_id, student_id, file_path, submitted_at,
                       CASE 
                           WHEN grade IS NULL THEN NULL
                           WHEN grade = '' THEN NULL
                           WHEN CAST(grade AS INTEGER) BETWEEN 0 AND 100 THEN CAST(grade AS INTEGER)
                           ELSE NULL
                       END,
                       feedback, graded_at
                FROM submissions
            """)
            
            # Replace old table
            cursor.execute("DROP TABLE submissions")
            cursor.execute("ALTER TABLE submissions_new RENAME TO submissions")
            print("  ✅ Grade column converted to INTEGER")
        else:
            print("  ✅ Grade column already correct type")
        
        # Check notifications table for enhanced fields
        cursor.execute("PRAGMA table_info(notifications)")
        notifications_columns = [col[1] for col in cursor.fetchall()]
        
        enhanced_fields = ['title', 'notification_type', 'target_user_id', 'expires_at', 'is_active']
        missing_fields = [field for field in enhanced_fields if field not in notifications_columns]
        
        if missing_fields:
            print(f"  🔄 Enhancing notifications table with fields: {', '.join(missing_fields)}")
            
            # Create new enhanced notifications table
            cursor.execute("""
                CREATE TABLE notifications_new (
                    id INTEGER PRIMARY KEY,
                    title VARCHAR(128) NOT NULL,
                    message TEXT NOT NULL,
                    notification_type VARCHAR(20) DEFAULT 'info',
                    sender_id INTEGER NOT NULL,
                    recipient_role VARCHAR(20),
                    target_user_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (sender_id) REFERENCES users (id),
                    FOREIGN KEY (target_user_id) REFERENCES users (id)
                )
            """)
            
            # Migrate existing notifications
            cursor.execute("""
                INSERT INTO notifications_new (id, title, message, notification_type, sender_id, recipient_role, created_at, is_active)
                SELECT id, 
                       COALESCE(SUBSTR(message, 1, 50) || '...', 'System Notification') as title,
                       message,
                       'info' as notification_type,
                       sender_id,
                       CASE 
                           WHEN recipient_role = 'all' THEN NULL
                           ELSE recipient_role
                       END,
                       created_at,
                       1 as is_active
                FROM notifications
            """)
            
            # Replace old table
            cursor.execute("DROP TABLE notifications")
            cursor.execute("ALTER TABLE notifications_new RENAME TO notifications")
            print("  ✅ Notifications table enhanced")
        else:
            print("  ✅ Notifications table already enhanced")
        
        # Add some sample enhanced notifications for testing
        print("  📢 Adding sample notifications...")
        
        # Get admin user ID
        cursor.execute("SELECT id FROM users WHERE role = 'admin' LIMIT 1")
        admin_result = cursor.fetchone()
        
        if admin_result:
            admin_id = admin_result[0]
            
            sample_notifications = [
                {
                    'title': 'Welcome to Phase 3!',
                    'message': 'The school management system has been enhanced with new features including improved grading, file management, and notification system.',
                    'type': 'success',
                    'role': None
                },
                {
                    'title': 'Assignment Submission Reminder',
                    'message': 'Students: Please remember to submit your assignments before the due date. Late submissions may result in grade penalties.',
                    'type': 'warning',
                    'role': 'student'
                },
                {
                    'title': 'New Grading System',
                    'message': 'Teachers: The new enhanced grading system is now available. You can provide detailed feedback and track student progress more effectively.',
                    'type': 'info',
                    'role': 'teacher'
                }
            ]
            
            for notif in sample_notifications:
                cursor.execute("""
                    INSERT INTO notifications (title, message, notification_type, sender_id, recipient_role, is_active)
                    VALUES (?, ?, ?, ?, ?, 1)
                """, (notif['title'], notif['message'], notif['type'], admin_id, notif['role']))
            
            print(f"  ✅ Added {len(sample_notifications)} sample notifications")
        
        # Commit all changes
        conn.commit()
        print("✅ Database migration completed successfully!")
        
        # Display migration summary
        print("\n📊 Migration Summary:")
        print("  ✅ Enhanced submissions table with grading timestamps")
        print("  ✅ Converted grade column to INTEGER type")
        print("  ✅ Enhanced notifications table with new features")
        print("  ✅ Added sample notifications for testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_migration():
    """Verify that the migration was successful."""
    db_path = os.path.join('instance', 'database.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔍 Verifying migration...")
        
        # Check submissions table
        cursor.execute("PRAGMA table_info(submissions)")
        submissions_columns = [col[1] for col in cursor.fetchall()]
        
        required_submission_fields = ['id', 'assignment_id', 'student_id', 'file_path', 'submitted_at', 'grade', 'feedback', 'graded_at']
        missing_submission_fields = [field for field in required_submission_fields if field not in submissions_columns]
        
        if missing_submission_fields:
            print(f"  ❌ Missing submission fields: {missing_submission_fields}")
            return False
        else:
            print("  ✅ Submissions table schema correct")
        
        # Check notifications table
        cursor.execute("PRAGMA table_info(notifications)")
        notifications_columns = [col[1] for col in cursor.fetchall()]
        
        required_notification_fields = ['id', 'title', 'message', 'notification_type', 'sender_id', 'recipient_role', 'target_user_id', 'created_at', 'expires_at', 'is_active']
        missing_notification_fields = [field for field in required_notification_fields if field not in notifications_columns]
        
        if missing_notification_fields:
            print(f"  ❌ Missing notification fields: {missing_notification_fields}")
            return False
        else:
            print("  ✅ Notifications table schema correct")
        
        # Check data integrity
        cursor.execute("SELECT COUNT(*) FROM notifications WHERE is_active = 1")
        active_notifications = cursor.fetchone()[0]
        print(f"  ✅ Found {active_notifications} active notifications")
        
        cursor.execute("SELECT COUNT(*) FROM submissions")
        total_submissions = cursor.fetchone()[0]
        print(f"  ✅ Found {total_submissions} submissions in database")
        
        print("✅ Migration verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False
    finally:
        conn.close()

def main():
    """Main migration function."""
    print("🚀 Phase 3 Database Migration")
    print("=" * 50)
    
    # Perform migration
    if migrate_database():
        # Verify migration
        if verify_migration():
            print("\n🎉 Phase 3 database migration completed successfully!")
            print("\n📋 What's New:")
            print("  • Enhanced grading system with timestamps")
            print("  • Improved notification system with targeting")
            print("  • Better data types for grades (INTEGER)")
            print("  • Sample notifications for testing")
            print("\n🌐 Ready to test Phase 3 features!")
        else:
            print("\n❌ Migration verification failed. Please check the database manually.")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
