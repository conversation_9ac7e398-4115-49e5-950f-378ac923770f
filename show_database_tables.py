#!/usr/bin/env python3
"""
Script to display all database tables and their contents.
"""

import sqlite3
import os
from datetime import datetime

def show_database_tables():
    """Display all tables and their contents from the database."""
    
    # Database path
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at: {db_path}")
        return
    
    print("🗄️ FLASK SCHOOL MANAGEMENT SYSTEM - DATABASE TABLES")
    print("=" * 70)
    print(f"📍 Database Location: {db_path}")
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
        tables = cursor.fetchall()
        
        print(f"\n📊 TOTAL TABLES: {len(tables)}")
        print("-" * 70)
        
        for table_name in tables:
            table = table_name[0]
            print(f"\n🔹 TABLE: {table.upper()}")
            print("-" * 40)
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table});")
            columns = cursor.fetchall()
            
            print("📋 COLUMNS:")
            for col in columns:
                col_id, name, data_type, not_null, default, pk = col
                pk_indicator = " (PRIMARY KEY)" if pk else ""
                null_indicator = " NOT NULL" if not_null else ""
                default_indicator = f" DEFAULT {default}" if default else ""
                print(f"   • {name}: {data_type}{pk_indicator}{null_indicator}{default_indicator}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"📈 TOTAL ROWS: {count}")
            
            # Show sample data (first 5 rows)
            if count > 0:
                cursor.execute(f"SELECT * FROM {table} LIMIT 5;")
                rows = cursor.fetchall()
                
                print("📄 SAMPLE DATA (First 5 rows):")
                column_names = [desc[1] for desc in columns]
                
                # Print header
                header = " | ".join([name[:15].ljust(15) for name in column_names])
                print(f"   {header}")
                print(f"   {'-' * len(header)}")
                
                # Print rows
                for row in rows:
                    formatted_row = []
                    for item in row:
                        if item is None:
                            formatted_row.append("NULL".ljust(15))
                        else:
                            str_item = str(item)
                            if len(str_item) > 15:
                                str_item = str_item[:12] + "..."
                            formatted_row.append(str_item.ljust(15))
                    
                    print(f"   {' | '.join(formatted_row)}")
                
                if count > 5:
                    print(f"   ... and {count - 5} more rows")
            else:
                print("📄 NO DATA - Table is empty")
            
            print("-" * 70)
        
        # Show database statistics
        print(f"\n📊 DATABASE STATISTICS:")
        print("-" * 40)
        
        # User statistics
        cursor.execute("SELECT role, COUNT(*) FROM user GROUP BY role;")
        user_stats = cursor.fetchall()
        print("👥 USERS BY ROLE:")
        for role, count in user_stats:
            print(f"   • {role.title()}: {count}")
        
        # Approval statistics
        cursor.execute("SELECT is_approved, COUNT(*) FROM user GROUP BY is_approved;")
        approval_stats = cursor.fetchall()
        print("✅ APPROVAL STATUS:")
        for approved, count in approval_stats:
            status = "Approved" if approved else "Pending"
            print(f"   • {status}: {count}")
        
        # Class and assignment statistics
        cursor.execute("SELECT COUNT(*) FROM class;")
        class_count = cursor.fetchone()[0]
        print(f"🏫 Total Classes: {class_count}")
        
        cursor.execute("SELECT COUNT(*) FROM assignment;")
        assignment_count = cursor.fetchone()[0]
        print(f"📝 Total Assignments: {assignment_count}")
        
        cursor.execute("SELECT COUNT(*) FROM submission;")
        submission_count = cursor.fetchone()[0]
        print(f"📤 Total Submissions: {submission_count}")
        
        cursor.execute("SELECT COUNT(*) FROM class_enrollment;")
        enrollment_count = cursor.fetchone()[0]
        print(f"📚 Total Enrollments: {enrollment_count}")
        
        conn.close()
        
        print("\n" + "=" * 70)
        print("✅ Database analysis complete!")
        print("🌐 You can also view this data at: http://127.0.0.1:5000/admin/database")
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ Error accessing database: {str(e)}")

if __name__ == "__main__":
    show_database_tables()
