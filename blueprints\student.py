from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from functools import wraps
from models import db, User, Student, ClassEnrollment, Assignment, Submission, Class
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from flask_wtf import FlaskForm
from wtforms import StringField, DateField, SubmitField, SelectField, FileField
from wtforms.validators import DataRequired, Length, ValidationError
from flask_wtf.file import FileAllowed

# Create the student blueprint with explicit name
student = Blueprint('student', __name__)

# Student access decorator
def student_required(f):
    """Decorator to require student role for a route."""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'student':
            flash('Access denied. Student privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Student profile form
class StudentProfileForm(FlaskForm):
    """Form for creating/editing student profile."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    dob = DateField('Date of Birth', format='%Y-%m-%d', validators=[DataRequired()])
    grade = StringField('Grade', validators=[DataRequired(), Length(max=10)])
    submit = SubmitField('Save Profile')

# Submission form
class SubmissionForm(FlaskForm):
    """Form for submitting assignments."""
    file = FileField('Assignment File', validators=[
        DataRequired(),
        FileAllowed(['pdf', 'png', 'jpg', 'jpeg'], 'Only PDF and image files (PNG, JPG, JPEG) are allowed')
    ])
    submit = SubmitField('Submit Assignment')

    def validate_file(self, file):
        """Validate file size (max 10MB)."""
        if file.data:
            # Check file size (10MB = 10 * 1024 * 1024 bytes)
            file.data.seek(0, 2)  # Seek to end of file
            file_size = file.data.tell()
            file.data.seek(0)  # Reset to beginning

            if file_size > 10 * 1024 * 1024:
                raise ValidationError('File size must be less than 10MB')

# Student dashboard route
@student.route('/dashboard')
@student_required
def dashboard():
    """Student dashboard page."""
    # Get the student profile
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get classes for this student
    enrollments = ClassEnrollment.query.filter_by(student_id=student_profile.id).all()
    classes_count = len(enrollments)

    # Get assignments for this student
    assignments = []
    for enrollment in enrollments:
        class_assignments = Assignment.query.filter_by(class_id=enrollment.class_id).all()
        assignments.extend(class_assignments)

    assignments_count = len(assignments)

    # Get completed assignments
    completed_count = 0
    for assignment in assignments:
        submission = Submission.query.filter_by(
            assignment_id=assignment.id,
            student_id=student_profile.id
        ).first()
        if submission:
            completed_count += 1

    # Calculate average grade
    submissions_with_grades = Submission.query.filter(
        Submission.student_id == student_profile.id,
        Submission.grade != None
    ).all()

    if submissions_with_grades:
        grades_sum = sum(s.grade for s in submissions_with_grades if s.grade is not None)
        average_grade = f"{grades_sum / len(submissions_with_grades):.1f}"
    else:
        average_grade = "N/A"

    return render_template('dashboards/student.html',
                          student=student_profile,
                          classes_count=classes_count,
                          assignments_count=assignments_count,
                          completed_count=completed_count,
                          average_grade=average_grade)

# Student profile route
@student.route('/profile', methods=['GET', 'POST'])
@student_required
def profile():
    """Create or edit student profile."""
    # Check if profile already exists
    student_profile = Student.query.filter_by(user_id=current_user.id).first()
    form = StudentProfileForm(obj=student_profile)

    if form.validate_on_submit():
        if student_profile:
            # Update existing profile
            student_profile.first_name = form.first_name.data
            student_profile.last_name = form.last_name.data
            student_profile.dob = form.dob.data
            student_profile.grade = form.grade.data
            flash('Student profile updated successfully!', 'success')
        else:
            # Create new student profile
            student_profile = Student(
                user_id=current_user.id,
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                dob=form.dob.data,
                grade=form.grade.data
            )
            db.session.add(student_profile)
            flash('Student profile created successfully!', 'success')

        db.session.commit()
        return redirect(url_for('student.dashboard'))

    return render_template('student/profile.html', form=form, student=student_profile)

# Student classes route
@student.route('/classes')
@student_required
def classes():
    """List of classes for the student."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get classes for this student
    enrollments = ClassEnrollment.query.filter_by(student_id=student_profile.id).all()

    return render_template('student/classes.html', enrollments=enrollments)

# Student assignments route
@student.route('/assignments')
@student_required
def assignments():
    """List of assignments for the student."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get classes for this student
    enrollments = ClassEnrollment.query.filter_by(student_id=student_profile.id).all()

    # Get assignments for this student
    assignments_data = []
    for enrollment in enrollments:
        class_assignments = Assignment.query.filter_by(class_id=enrollment.class_id).all()
        for assignment in class_assignments:
            submission = Submission.query.filter_by(
                assignment_id=assignment.id,
                student_id=student_profile.id
            ).first()

            assignments_data.append({
                'assignment': assignment,
                'class': enrollment.class_,
                'submission': submission
            })

    return render_template('student/assignments.html', assignments=assignments_data)

# Submit assignment route
@student.route('/assignment/<int:assignment_id>/submit', methods=['GET', 'POST'])
@student_required
def submit_assignment(assignment_id):
    """Submit an assignment."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    assignment = Assignment.query.get_or_404(assignment_id)

    # Check if student is enrolled in the class
    enrollment = ClassEnrollment.query.filter_by(
        student_id=student_profile.id,
        class_id=assignment.class_id
    ).first()

    if not enrollment:
        flash('You are not enrolled in this class.', 'danger')
        return redirect(url_for('student.assignments'))

    # Check if assignment is past due
    if assignment.due_date < datetime.now():
        flash('This assignment is past due and cannot be submitted.', 'danger')
        return redirect(url_for('student.assignments'))

    # Check if already submitted
    existing_submission = Submission.query.filter_by(
        assignment_id=assignment_id,
        student_id=student_profile.id
    ).first()

    if existing_submission:
        flash('You have already submitted this assignment.', 'warning')
        return redirect(url_for('student.assignments'))

    form = SubmissionForm()

    if form.validate_on_submit():
        file = form.file.data
        if file:
            # Check if student already submitted this assignment
            existing_submission = Submission.query.filter_by(
                assignment_id=assignment_id,
                student_id=student_profile.id
            ).first()

            if existing_submission:
                flash('You have already submitted this assignment. Contact your teacher to resubmit.', 'warning')
                return redirect(url_for('student.assignments'))

            filename = secure_filename(file.filename)
            # Create unique filename with student and assignment info
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"student_{student_profile.id}_assignment_{assignment_id}_{timestamp}_{filename}"
            file_path = os.path.join(current_app.config['ASSIGNMENT_UPLOAD_FOLDER'], filename)

            # Save file
            file.save(file_path)

            # Create submission record
            submission = Submission(
                assignment_id=assignment_id,
                student_id=student_profile.id,
                file_path=filename
            )

            db.session.add(submission)
            db.session.commit()

            flash('Assignment submitted successfully!', 'success')
            return redirect(url_for('student.assignments'))

    return render_template('student/submit_assignment.html', form=form, assignment=assignment)

# Student schedule route
@student.route('/schedule')
@student_required
def schedule():
    """Display student's class schedule."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get student's enrolled classes
    enrollments = ClassEnrollment.query.filter_by(student_id=student_profile.id).all()
    classes = [enrollment.class_ for enrollment in enrollments]

    # Organize classes by day of week
    schedule_data = {}
    for class_obj in classes:
        day_name = class_obj.schedule_time.strftime('%A')
        if day_name not in schedule_data:
            schedule_data[day_name] = []
        schedule_data[day_name].append({
            'time': class_obj.schedule_time.strftime('%H:%M'),
            'subject': class_obj.subject,
            'teacher': f"{class_obj.teacher.first_name} {class_obj.teacher.last_name}",
            'room': class_obj.room
        })

    # Sort classes by time for each day
    for day in schedule_data:
        schedule_data[day].sort(key=lambda x: x['time'])

    return render_template('student/schedule.html',
                         schedule=schedule_data,
                         student=student_profile)

# Student grades route
@student.route('/grades')
@student_required
def grades():
    """Display student's final grades."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get student's submissions with grades
    submissions = Submission.query.filter_by(student_id=student_profile.id).filter(
        Submission.grade.isnot(None)
    ).all()

    # Organize grades by subject
    grades_by_subject = {}
    for submission in submissions:
        subject = submission.assignment.class_.subject
        if subject not in grades_by_subject:
            grades_by_subject[subject] = {
                'teacher': f"{submission.assignment.class_.teacher.first_name} {submission.assignment.class_.teacher.last_name}",
                'assignments': []
            }
        grades_by_subject[subject]['assignments'].append({
            'title': submission.assignment.title,
            'grade': submission.grade,
            'feedback': submission.feedback,
            'submitted_at': submission.submitted_at
        })

    return render_template('student/grades.html',
                         grades=grades_by_subject,
                         student=student_profile)

# Student feedback route
@student.route('/feedback')
@student_required
def feedback():
    """Display student's performance feedback."""
    student_profile = Student.query.filter_by(user_id=current_user.id).first()

    if not student_profile:
        flash('Please complete your student profile first.', 'warning')
        return redirect(url_for('student.profile'))

    # Get student's submissions with feedback
    submissions = Submission.query.filter_by(student_id=student_profile.id).filter(
        Submission.feedback.isnot(None)
    ).all()

    # Organize feedback by subject
    feedback_by_subject = {}
    for submission in submissions:
        subject = submission.assignment.class_.subject
        if subject not in feedback_by_subject:
            feedback_by_subject[subject] = {
                'teacher': f"{submission.assignment.class_.teacher.first_name} {submission.assignment.class_.teacher.last_name}",
                'feedback_items': []
            }
        feedback_by_subject[subject]['feedback_items'].append({
            'assignment': submission.assignment.title,
            'grade': submission.grade,
            'feedback': submission.feedback,
            'submitted_at': submission.submitted_at
        })

    return render_template('student/feedback.html',
                         feedback=feedback_by_subject,
                         student=student_profile)
