from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from flask_wtf import <PERSON>laskForm
from wtforms import StringField, PasswordField, SelectField, SubmitField, EmailField, DateField, TextAreaField, IntegerField, FieldList, FormField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, NumberRange
from models import db, User, Student, Teacher, Parent
from datetime import datetime
import re
import json

# Create the auth blueprint with explicit name
auth = Blueprint('auth', __name__)

# Custom password validator
class StrongPassword:
    """Custom validator for strong password requirements."""

    def __init__(self, message=None):
        if not message:
            message = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character'
        self.message = message

    def __call__(self, form, field):
        password = field.data
        if not password:
            return

        # Check minimum length
        if len(password) < 8:
            raise ValidationError('Password must be at least 8 characters long')

        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            raise ValidationError('Password must contain at least one uppercase letter (A-Z)')

        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            raise ValidationError('Password must contain at least one lowercase letter (a-z)')

        # Check for digit
        if not re.search(r'[0-9]', password):
            raise ValidationError('Password must contain at least one number (0-9)')

        # Check for special character
        if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
            raise ValidationError('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)')

# Child information form for dynamic parent registration
class ChildForm(FlaskForm):
    """Form for individual child information."""
    child_name = StringField('Child Full Name', validators=[DataRequired(), Length(max=128)])
    child_grade = SelectField('Child Grade', choices=[
        ('K', 'Kindergarten'),
        ('1', 'Grade 1'),
        ('2', 'Grade 2'),
        ('3', 'Grade 3'),
        ('4', 'Grade 4'),
        ('5', 'Grade 5'),
        ('6', 'Grade 6'),
        ('7', 'Grade 7'),
        ('8', 'Grade 8'),
        ('9', 'Grade 9'),
        ('10', 'Grade 10'),
        ('11', 'Grade 11'),
        ('12', 'Grade 12')
    ], validators=[DataRequired()])

# Base registration form
class BaseRegistrationForm(FlaskForm):
    """Base form for user registration with common fields."""
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=64)])
    email = EmailField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    password = PasswordField('Password', validators=[DataRequired(), StrongPassword()])
    confirm = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])

    def validate_username(self, username):
        """Validate that username is unique."""
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Username already taken. Please choose a different one.')

    def validate_email(self, email):
        """Validate that email is unique."""
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Email already registered. Please use a different one.')

# General registration form (role selector)
class RegistrationForm(BaseRegistrationForm):
    """Form for general user registration with role selection."""
    role = SelectField('Role', choices=[
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('parent', 'Parent')
    ])
    submit = SubmitField('Register')

# Admin registration form
class AdminRegistrationForm(BaseRegistrationForm):
    """Form for admin registration with security code."""
    admin_code = StringField('Admin Code', validators=[DataRequired()],
                            render_kw={"placeholder": "Enter admin registration code"})
    submit = SubmitField('Register as Admin')

    def validate_admin_code(self, admin_code):
        """Validate admin registration code."""
        # In production, this should be a secure, configurable code
        if admin_code.data != 'ADMIN2024':
            raise ValidationError('Invalid admin registration code.')

# Teacher registration form
class TeacherRegistrationForm(BaseRegistrationForm):
    """Form for teacher registration with profile information."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    subjects = TextAreaField('Subjects Taught', validators=[DataRequired()],
                           render_kw={"placeholder": "Enter subjects separated by commas (e.g., Mathematics, Physics, Chemistry)"})
    years_experience = IntegerField('Years of Experience', validators=[DataRequired(), NumberRange(min=0, max=50)],
                                  render_kw={"placeholder": "Enter years of teaching experience"})
    qualifications = TextAreaField('Qualifications', validators=[DataRequired()],
                                 render_kw={"placeholder": "List your degrees, certifications, and relevant qualifications"})
    submit = SubmitField('Register as Teacher')

# Student registration form
class StudentRegistrationForm(BaseRegistrationForm):
    """Form for student registration with profile information."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    dob = DateField('Date of Birth', validators=[DataRequired()])
    grade = SelectField('Grade', choices=[
        ('K', 'Kindergarten'),
        ('1', 'Grade 1'),
        ('2', 'Grade 2'),
        ('3', 'Grade 3'),
        ('4', 'Grade 4'),
        ('5', 'Grade 5'),
        ('6', 'Grade 6'),
        ('7', 'Grade 7'),
        ('8', 'Grade 8'),
        ('9', 'Grade 9'),
        ('10', 'Grade 10'),
        ('11', 'Grade 11'),
        ('12', 'Grade 12')
    ], validators=[DataRequired()])
    parent_id = SelectField('Parent (Optional)', coerce=int, choices=[], validate_choice=False)
    submit = SubmitField('Register as Student')

    def __init__(self, *args, **kwargs):
        super(StudentRegistrationForm, self).__init__(*args, **kwargs)
        # Populate parent choices
        self.parent_id.choices = [(0, 'No Parent Selected')] + [
            (p.id, f"{p.first_name} {p.last_name}")
            for p in Parent.query.all()
        ]

# Parent registration form
class ParentRegistrationForm(BaseRegistrationForm):
    """Form for parent registration with profile information."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    phone = StringField('Phone Number', validators=[DataRequired(), Length(max=20)],
                       render_kw={"placeholder": "Enter your phone number"})
    relationship_type = SelectField('Relationship Type', choices=[
        ('Father', 'Father'),
        ('Mother', 'Mother'),
        ('Guardian', 'Guardian'),
        ('Other', 'Other')
    ], validators=[DataRequired()])
    num_children = SelectField('Number of Children', choices=[
        ('1', '1 Child'),
        ('2', '2 Children'),
        ('3', '3 Children'),
        ('4', '4 Children'),
        ('5', '5 Children')
    ], validators=[DataRequired()], default='1')

    # Dynamic child fields (will be populated by JavaScript)
    child_1_name = StringField('Child 1 Full Name', validators=[DataRequired(), Length(max=128)])
    child_1_grade = SelectField('Child 1 Grade', choices=[
        ('K', 'Kindergarten'), ('1', 'Grade 1'), ('2', 'Grade 2'), ('3', 'Grade 3'),
        ('4', 'Grade 4'), ('5', 'Grade 5'), ('6', 'Grade 6'), ('7', 'Grade 7'),
        ('8', 'Grade 8'), ('9', 'Grade 9'), ('10', 'Grade 10'), ('11', 'Grade 11'), ('12', 'Grade 12')
    ], validators=[DataRequired()])

    child_2_name = StringField('Child 2 Full Name', validators=[Length(max=128)])
    child_2_grade = SelectField('Child 2 Grade', choices=[
        ('', 'Select Grade'), ('K', 'Kindergarten'), ('1', 'Grade 1'), ('2', 'Grade 2'), ('3', 'Grade 3'),
        ('4', 'Grade 4'), ('5', 'Grade 5'), ('6', 'Grade 6'), ('7', 'Grade 7'),
        ('8', 'Grade 8'), ('9', 'Grade 9'), ('10', 'Grade 10'), ('11', 'Grade 11'), ('12', 'Grade 12')
    ])

    child_3_name = StringField('Child 3 Full Name', validators=[Length(max=128)])
    child_3_grade = SelectField('Child 3 Grade', choices=[
        ('', 'Select Grade'), ('K', 'Kindergarten'), ('1', 'Grade 1'), ('2', 'Grade 2'), ('3', 'Grade 3'),
        ('4', 'Grade 4'), ('5', 'Grade 5'), ('6', 'Grade 6'), ('7', 'Grade 7'),
        ('8', 'Grade 8'), ('9', 'Grade 9'), ('10', 'Grade 10'), ('11', 'Grade 11'), ('12', 'Grade 12')
    ])

    child_4_name = StringField('Child 4 Full Name', validators=[Length(max=128)])
    child_4_grade = SelectField('Child 4 Grade', choices=[
        ('', 'Select Grade'), ('K', 'Kindergarten'), ('1', 'Grade 1'), ('2', 'Grade 2'), ('3', 'Grade 3'),
        ('4', 'Grade 4'), ('5', 'Grade 5'), ('6', 'Grade 6'), ('7', 'Grade 7'),
        ('8', 'Grade 8'), ('9', 'Grade 9'), ('10', 'Grade 10'), ('11', 'Grade 11'), ('12', 'Grade 12')
    ])

    child_5_name = StringField('Child 5 Full Name', validators=[Length(max=128)])
    child_5_grade = SelectField('Child 5 Grade', choices=[
        ('', 'Select Grade'), ('K', 'Kindergarten'), ('1', 'Grade 1'), ('2', 'Grade 2'), ('3', 'Grade 3'),
        ('4', 'Grade 4'), ('5', 'Grade 5'), ('6', 'Grade 6'), ('7', 'Grade 7'),
        ('8', 'Grade 8'), ('9', 'Grade 9'), ('10', 'Grade 10'), ('11', 'Grade 11'), ('12', 'Grade 12')
    ])

    submit = SubmitField('Register as Parent')

# Login route
@auth.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    # If user is already authenticated, redirect to home
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Validate form inputs
        if not username or not password:
            flash('Please enter both username and password.', 'danger')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            # Check if user is approved
            if not user.is_approved:
                flash('Your account is pending approval by an administrator.', 'warning')
                return redirect(url_for('auth.waiting_approval'))

            # Log in the user
            login_user(user)
            flash('Logged in successfully!', 'success')

            # Redirect based on role
            if user.role == 'admin':
                return redirect(url_for('admin.dashboard'))
            elif user.role == 'teacher':
                return redirect(url_for('teacher.dashboard'))
            elif user.role == 'student':
                return redirect(url_for('student.dashboard'))
            elif user.role == 'parent':
                return redirect(url_for('parent.dashboard'))
            else:
                return redirect(url_for('index'))
        else:
            flash('Invalid username or password. Please try again.', 'danger')

    return render_template('login.html')

# General registration route (role selector)
@auth.route('/register', methods=['GET', 'POST'])
def register():
    """Handle general user registration - shows role selection."""
    # If user is already authenticated, redirect to home
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    return render_template('auth/register.html')

# Admin registration route
@auth.route('/register/admin', methods=['GET', 'POST'])
def register_admin():
    """Handle admin registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = AdminRegistrationForm()

    if form.validate_on_submit():
        # Create new admin user
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='admin',
            is_approved=True  # Admins are auto-approved
        )
        user.set_password(form.password.data)

        db.session.add(user)
        db.session.commit()

        flash('Admin registration successful! You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register_admin.html', form=form)

# Teacher registration route
@auth.route('/register/teacher', methods=['GET', 'POST'])
def register_teacher():
    """Handle teacher registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = TeacherRegistrationForm()

    if form.validate_on_submit():
        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='teacher',
            is_approved=False  # Require admin approval
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.flush()  # Get user ID

        # Create teacher profile
        teacher = Teacher(
            user_id=user.id,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            subjects=form.subjects.data,
            years_experience=form.years_experience.data,
            qualifications=form.qualifications.data
        )
        db.session.add(teacher)
        db.session.commit()

        flash('Teacher registration successful! Your account is pending approval by an administrator.', 'success')
        return redirect(url_for('auth.waiting_approval'))

    return render_template('auth/register_teacher.html', form=form)

# Student registration route
@auth.route('/register/student', methods=['GET', 'POST'])
def register_student():
    """Handle student registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = StudentRegistrationForm()

    if form.validate_on_submit():
        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='student',
            is_approved=False  # Require admin approval
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.flush()  # Get user ID

        # Create student profile
        parent_id = form.parent_id.data if form.parent_id.data != 0 else None
        student = Student(
            user_id=user.id,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            dob=form.dob.data,
            grade=form.grade.data,
            parent_id=parent_id
        )
        db.session.add(student)
        db.session.commit()

        flash('Student registration successful! Your account is pending approval by an administrator.', 'success')
        return redirect(url_for('auth.waiting_approval'))

    return render_template('auth/register_student.html', form=form)

# Parent registration route
@auth.route('/register/parent', methods=['GET', 'POST'])
def register_parent():
    """Handle parent registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = ParentRegistrationForm()

    if form.validate_on_submit():
        # Collect children information
        children_info = []
        num_children = int(form.num_children.data)

        for i in range(1, num_children + 1):
            child_name_field = getattr(form, f'child_{i}_name')
            child_grade_field = getattr(form, f'child_{i}_grade')

            if child_name_field.data and child_grade_field.data:
                children_info.append({
                    'name': child_name_field.data,
                    'grade': child_grade_field.data
                })

        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='parent',
            is_approved=False  # Require admin approval
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.flush()  # Get user ID

        # Create parent profile
        parent = Parent(
            user_id=user.id,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data,
            relationship_type=form.relationship_type.data,
            children_info=json.dumps(children_info) if children_info else None
        )
        db.session.add(parent)
        db.session.commit()

        flash('Parent registration successful! Your account is pending approval by an administrator.', 'success')
        return redirect(url_for('auth.waiting_approval'))

    return render_template('auth/register_parent.html', form=form)

# Logout route
@auth.route('/logout')
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

# Waiting for approval route
@auth.route('/waiting-approval')
def waiting_approval():
    """Display waiting for approval page."""
    return render_template('waiting_approval.html')
