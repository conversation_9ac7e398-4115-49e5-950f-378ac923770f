#!/usr/bin/env python3
"""
Database migration script to add attachment_path field to Assignment model.
Run this once to update the database schema.
"""

import sqlite3
import os

def migrate_database():
    """Add attachment_path column to assignments table."""
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print("❌ Database file not found. Please run the application first to create the database.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(assignments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'attachment_path' in columns:
            print("✅ attachment_path column already exists in assignments table")
            conn.close()
            return True
        
        # Add the new column
        cursor.execute("ALTER TABLE assignments ADD COLUMN attachment_path VARCHAR(255)")
        conn.commit()
        
        print("✅ Successfully added attachment_path column to assignments table")
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(assignments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'attachment_path' in columns:
            print("✅ Migration verified successfully")
        else:
            print("❌ Migration verification failed")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def main():
    """Run the migration."""
    print("🔄 Running Assignment Attachment Migration")
    print("=" * 50)
    
    success = migrate_database()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("✅ Assignment model now supports file attachments")
        print("\n📋 Next steps:")
        print("1. Restart the Flask server")
        print("2. Test assignment creation with file attachments")
        print("3. Verify file download functionality")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
