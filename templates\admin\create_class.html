{% extends "base.html" %}

{% block title %}{{ title }} - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
.class-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.class-form {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 2em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5em;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-control {
    width: 100%;
    padding: 0.75em;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-error {
    color: var(--danger);
    font-size: 0.9em;
    margin-top: 0.5em;
}

.form-help {
    font-size: 0.9em;
    color: var(--text-secondary);
    margin-top: 0.5em;
}

.students-selection {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 0.5em;
    background: var(--bg);
}

.student-option {
    display: flex;
    align-items: center;
    padding: 0.5em;
    margin: 0.25em 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.student-option:hover {
    background: var(--hover-bg);
}

.student-option input[type="checkbox"] {
    margin-right: 0.75em;
    transform: scale(1.2);
}

.student-option label {
    margin: 0;
    cursor: pointer;
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

.btn {
    padding: 0.75em 1.5em;
    border: none;
    border-radius: 4px;
    font-size: 1em;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-dark);
}

.class-info {
    background: #f8f9fa;
    padding: 1em;
    border-radius: 6px;
    margin-bottom: 2em;
    border-left: 4px solid var(--primary);
}

@media (max-width: 768px) {
    .class-form-container {
        padding: 1em;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="class-form-container">
    <div class="dashboard-header">
        <h1>🏫 {{ title }}</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin.schedules') }}" class="btn btn-secondary">
                ← Back to Schedules
            </a>
        </div>
    </div>

    {% if class_obj %}
    <div class="class-info">
        <h3>Editing Class Information</h3>
        <p><strong>Current Subject:</strong> {{ class_obj.subject }}</p>
        <p><strong>Current Teacher:</strong> {{ class_obj.teacher.first_name }} {{ class_obj.teacher.last_name }}</p>
        <p><strong>Current Room:</strong> {{ class_obj.room }}</p>
        <p><strong>Current Enrollments:</strong> {{ class_obj.enrollments.count() }} students</p>
    </div>
    {% endif %}

    <form method="POST" class="class-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.subject.label(class="form-label") }}
            {{ form.subject(class="form-control", placeholder="e.g., Mathematics, English Literature, Physics") }}
            {% if form.subject.errors %}
                <div class="form-error">
                    {% for error in form.subject.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Enter the subject name for this class</small>
        </div>

        <div class="form-group">
            {{ form.teacher_id.label(class="form-label") }}
            {{ form.teacher_id(class="form-control") }}
            {% if form.teacher_id.errors %}
                <div class="form-error">
                    {% for error in form.teacher_id.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the teacher who will teach this class</small>
        </div>

        <div class="form-group">
            {{ form.schedule_time.label(class="form-label") }}
            {{ form.schedule_time(class="form-control") }}
            {% if form.schedule_time.errors %}
                <div class="form-error">
                    {% for error in form.schedule_time.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the day and time when this class will be held</small>
        </div>

        <div class="form-group">
            {{ form.room.label(class="form-label") }}
            {{ form.room(class="form-control", placeholder="e.g., Room 101, Lab A, Gymnasium") }}
            {% if form.room.errors %}
                <div class="form-error">
                    {% for error in form.room.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Enter the room or location where the class will be held</small>
        </div>

        <div class="form-group">
            {{ form.students.label(class="form-label") }}
            <div class="students-selection">
                {% for value, label in form.students.choices %}
                    <div class="student-option">
                        <input type="checkbox"
                               name="students"
                               value="{{ value }}"
                               id="student_{{ value }}"
                               {% if form.students.data and value in form.students.data %}checked{% endif %}>
                        <label for="student_{{ value }}">{{ label }}</label>
                    </div>
                {% endfor %}
            </div>
            {% if form.students.errors %}
                <div class="form-error">
                    {% for error in form.students.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select students to enroll in this class (optional - you can add students later)</small>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary") }}
            <a href="{{ url_for('admin.schedules') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add search functionality for students
    const studentsContainer = document.querySelector('.students-selection');
    if (studentsContainer) {
        // Create search input
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Search students...';
        searchInput.className = 'form-control';
        searchInput.style.marginBottom = '0.5em';
        
        studentsContainer.parentNode.insertBefore(searchInput, studentsContainer);
        
        // Filter students based on search
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const studentOptions = studentsContainer.querySelectorAll('.student-option');
            
            studentOptions.forEach(option => {
                const label = option.querySelector('label').textContent.toLowerCase();
                if (label.includes(searchTerm)) {
                    option.style.display = 'flex';
                } else {
                    option.style.display = 'none';
                }
            });
        });
    }
    
    // Add select all/none functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.type = 'button';
    selectAllBtn.textContent = 'Select All';
    selectAllBtn.className = 'btn btn-secondary';
    selectAllBtn.style.marginRight = '0.5em';
    selectAllBtn.style.marginBottom = '0.5em';
    
    const selectNoneBtn = document.createElement('button');
    selectNoneBtn.type = 'button';
    selectNoneBtn.textContent = 'Select None';
    selectNoneBtn.className = 'btn btn-secondary';
    selectNoneBtn.style.marginBottom = '0.5em';
    
    if (studentsContainer) {
        const buttonsDiv = document.createElement('div');
        buttonsDiv.appendChild(selectAllBtn);
        buttonsDiv.appendChild(selectNoneBtn);
        studentsContainer.parentNode.insertBefore(buttonsDiv, studentsContainer);
        
        selectAllBtn.addEventListener('click', function() {
            const checkboxes = studentsContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
        });
        
        selectNoneBtn.addEventListener('click', function() {
            const checkboxes = studentsContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        });
    }
});
</script>
{% endblock %}
