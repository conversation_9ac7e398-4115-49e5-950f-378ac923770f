from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone
from db import db

# Helper function to get current UTC time
def get_utc_now():
    """Return current time in UTC timezone."""
    return datetime.now(timezone.utc)

class User(db.Model, UserMixin):
    """User model representing all system users with role-based access."""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'admin', 'teacher', 'student', 'parent'
    is_approved = db.Column(db.<PERSON>, default=False)  # Field for admin approval
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Relationships
    student = db.relationship('Student', backref='user', uselist=False, cascade='all, delete-orphan')
    teacher = db.relationship('Teacher', backref='user', uselist=False, cascade='all, delete-orphan')
    parent = db.relationship('Parent', backref='user', uselist=False, cascade='all, delete-orphan')
    # Notifications will be accessed via backref from Notification model

    def set_password(self, password):
        """Set password hash from plain text password."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check if provided password matches stored hash."""
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'


class Student(db.Model):
    """Student model with personal information and class enrollments."""
    __tablename__ = 'students'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    grade = db.Column(db.String(10), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('parents.id'), nullable=True)

    # Relationships
    enrollments = db.relationship('ClassEnrollment', backref='student', lazy='dynamic', cascade='all, delete-orphan')
    submissions = db.relationship('Submission', backref='student', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Student {self.first_name} {self.last_name}>'


class Teacher(db.Model):
    """Teacher model with personal information and assigned classes."""
    __tablename__ = 'teachers'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    subjects = db.Column(db.String(255), nullable=False)  # Comma-separated list of subjects
    years_experience = db.Column(db.Integer, nullable=False, default=0)  # Years of teaching experience
    qualifications = db.Column(db.Text, nullable=False)  # Degrees, certifications, qualifications

    # Relationships
    classes = db.relationship('Class', backref='teacher', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Teacher {self.first_name} {self.last_name}>'


class Parent(db.Model):
    """Parent model with personal information and linked children."""
    __tablename__ = 'parents'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    relationship_type = db.Column(db.String(20), nullable=False, default='Parent')  # Father, Mother, Guardian, Other
    children_info = db.Column(db.Text, nullable=True)  # JSON string storing children information

    # Relationships
    children = db.relationship('Student', backref='parent', lazy='dynamic')

    def __repr__(self):
        return f'<Parent {self.first_name} {self.last_name}>'


class Class(db.Model):
    """Class model representing a course with schedule and assignments."""
    __tablename__ = 'classes'

    id = db.Column(db.Integer, primary_key=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('teachers.id'), nullable=False)
    subject = db.Column(db.String(64), nullable=False)
    schedule_time = db.Column(db.DateTime, nullable=False)
    room = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Relationships
    enrollments = db.relationship('ClassEnrollment', backref='class_', lazy='dynamic', cascade='all, delete-orphan')
    assignments = db.relationship('Assignment', backref='class_', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Class {self.subject}>'


class ClassEnrollment(db.Model):
    """ClassEnrollment model linking students to classes."""
    __tablename__ = 'class_enrollments'

    id = db.Column(db.Integer, primary_key=True)
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    enrolled_at = db.Column(db.DateTime, default=get_utc_now)

    def __repr__(self):
        return f'<ClassEnrollment {self.id}>'


class Assignment(db.Model):
    """Assignment model for tasks assigned to a class."""
    __tablename__ = 'assignments'

    id = db.Column(db.Integer, primary_key=True)
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'), nullable=False)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text, nullable=False)
    due_date = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Relationships
    submissions = db.relationship('Submission', backref='assignment', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Assignment {self.title}>'


class Submission(db.Model):
    """Submission model for student assignment submissions."""
    __tablename__ = 'submissions'

    id = db.Column(db.Integer, primary_key=True)
    assignment_id = db.Column(db.Integer, db.ForeignKey('assignments.id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    submitted_at = db.Column(db.DateTime, default=get_utc_now)
    grade = db.Column(db.Integer, nullable=True)  # Grade out of 100
    feedback = db.Column(db.Text, nullable=True)
    graded_at = db.Column(db.DateTime, nullable=True)  # When the submission was graded

    def __repr__(self):
        return f'<Submission {self.id}>'


class Notification(db.Model):
    """Enhanced notification model for system-wide and targeted announcements."""
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(20), nullable=False, default='info')  # info, warning, success, danger
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_role = db.Column(db.String(20), nullable=True)  # 'all', 'student', 'teacher', 'parent', or None for all
    target_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # For user-specific notifications
    created_at = db.Column(db.DateTime, default=get_utc_now)
    expires_at = db.Column(db.DateTime, nullable=True)  # Optional expiration date
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    sender = db.relationship('User', foreign_keys=[sender_id], backref='created_notifications')
    target_user = db.relationship('User', foreign_keys=[target_user_id], backref='received_notifications')

    def __repr__(self):
        return f'<Notification {self.title}>'

    def is_visible_to_user(self, user):
        """Check if notification should be visible to a specific user."""
        if not self.is_active:
            return False

        # Check expiration with proper timezone handling
        if self.expires_at and self.expires_at < get_utc_now():
            return False

        # User-specific notification
        if self.target_user_id:
            return self.target_user_id == user.id

        # Role-specific notification
        if self.recipient_role and self.recipient_role != 'all':
            return user.role == self.recipient_role

        # System-wide notification (recipient_role is 'all' or None)
        return True

    @staticmethod
    def get_notifications_for_user(user):
        """Get all notifications visible to a specific user."""
        all_notifications = Notification.query.filter_by(is_active=True).order_by(Notification.created_at.desc()).all()
        return [notification for notification in all_notifications if notification.is_visible_to_user(user)]
