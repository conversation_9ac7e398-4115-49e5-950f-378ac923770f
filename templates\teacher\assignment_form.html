{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{{ title }}</h2>
    
    <form method="POST" enctype="multipart/form-data" class="assignment-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.title.label(class="form-label") }}
            {{ form.title(class="form-control", placeholder="e.g., Chapter 5 Quiz, Essay Assignment") }}
            {% if form.title.errors %}
                <div class="form-error">
                    {% for error in form.title.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.class_id.label(class="form-label") }}
            {{ form.class_id(class="form-control") }}
            {% if form.class_id.errors %}
                <div class="form-error">
                    {% for error in form.class_id.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the class for this assignment</small>
        </div>

        <div class="form-group">
            {{ form.description.label(class="form-label") }}
            {{ form.description(class="form-control", rows="6", placeholder="Provide detailed instructions for the assignment...") }}
            {% if form.description.errors %}
                <div class="form-error">
                    {% for error in form.description.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.due_date.label(class="form-label") }}
            <div class="date-selector">
                {{ form.due_date(class="form-control date-select") }}
                <div class="date-info">
                    <span class="date-icon">📅</span>
                    <span class="date-help">Choose a date within the next 30 days</span>
                </div>
            </div>
            {% if form.due_date.errors %}
                <div class="form-error">
                    {% for error in form.due_date.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the due date for this assignment</small>
        </div>

        <div class="form-group">
            {{ form.due_time.label(class="form-label") }}
            <div class="time-selector">
                {{ form.due_time(class="form-control time-select") }}
                <div class="time-info">
                    <span class="time-icon">🕐</span>
                    <span class="time-help">Choose a specific time (30-minute intervals)</span>
                </div>
            </div>
            {% if form.due_time.errors %}
                <div class="form-error">
                    {% for error in form.due_time.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the due time for this assignment</small>
        </div>

        <div class="form-group">
            {{ form.attachment.label(class="form-label") }}
            <div class="file-upload-area">
                {{ form.attachment(class="form-control-file", id="attachment-input", accept=".pdf,.png,.jpg,.jpeg,.doc,.docx,.txt") }}
                <div class="file-upload-info">
                    <div class="upload-icon">📎</div>
                    <p class="upload-text">Choose a file or drag and drop</p>
                    <p class="upload-requirements">
                        <strong>Accepted formats:</strong> PDF, PNG, JPG, JPEG, DOC, DOCX, TXT<br>
                        <strong>Maximum size:</strong> 10MB
                    </p>
                </div>
            </div>
            {% if form.attachment.errors %}
                <div class="form-error">
                    {% for error in form.attachment.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <div id="attachment-preview" class="file-preview" style="display: none;">
                <div class="preview-content">
                    <span id="attachment-name"></span>
                    <span id="attachment-size"></span>
                    <button type="button" id="remove-attachment" class="remove-file-btn">×</button>
                </div>
            </div>
            <small class="form-help">Optional: Attach a file to this assignment (instructions, worksheets, etc.)</small>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('teacher.assignments') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.assignment-form {
    max-width: 700px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}

/* File Upload Styles */
.file-upload-area {
    position: relative;
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 2em;
    text-align: center;
    transition: all 0.3s ease;
    background: #fafafa;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: #f0f8ff;
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: #e6f3ff;
}

.form-control-file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-icon {
    font-size: 2em;
    margin-bottom: 0.5em;
}

.upload-text {
    font-size: 1.1em;
    margin: 0.5em 0;
    color: var(--text);
}

.upload-requirements {
    font-size: 0.9em;
    color: var(--text-secondary);
    margin: 0;
}

.file-preview {
    margin-top: 1em;
    padding: 1em;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
}

.preview-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-file-btn {
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
}

.remove-file-btn:hover {
    background: #cc0000;
}

/* Enhanced Date/Time Selector Styles */
.date-selector, .time-selector {
    position: relative;
}

.date-select, .time-select {
    padding-right: 3em;
}

.date-info, .time-info {
    position: absolute;
    right: 0.75em;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 0.5em;
    pointer-events: none;
    color: var(--text-secondary);
    font-size: 0.9em;
}

.date-icon, .time-icon {
    font-size: 1.2em;
}

.date-help, .time-help {
    font-size: 0.8em;
    white-space: nowrap;
}

.form-group .date-selector,
.form-group .time-selector {
    margin-bottom: 0.5em;
}

.date-time-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.75em;
    margin-top: 1em;
    display: none;
}

.date-time-preview.show {
    display: block;
}

.preview-label {
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 0.5em;
}

.preview-datetime {
    font-size: 1.1em;
    color: var(--text);
}

@media (max-width: 768px) {
    .date-info, .time-info {
        position: static;
        transform: none;
        margin-top: 0.5em;
        justify-content: center;
    }

    .date-select, .time-select {
        padding-right: 0.75em;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('attachment-input');
    const filePreview = document.getElementById('attachment-preview');
    const fileName = document.getElementById('attachment-name');
    const fileSize = document.getElementById('attachment-size');
    const removeBtn = document.getElementById('remove-attachment');
    const uploadArea = document.querySelector('.file-upload-area');

    if (!fileInput) return;

    // File input change event
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            displayFilePreview(file);
        }
    });

    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            displayFilePreview(files[0]);
        }
    });

    // Remove file button
    removeBtn.addEventListener('click', function() {
        removeFile();
    });

    function displayFilePreview(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        filePreview.style.display = 'block';

        // Validate file type and size
        const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg',
                             'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                             'text/plain'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid file type (PDF, images, DOC, DOCX, TXT)');
            removeFile();
            return;
        }

        if (file.size > maxSize) {
            alert('File size must be less than 10MB');
            removeFile();
            return;
        }
    }

    function removeFile() {
        fileInput.value = '';
        filePreview.style.display = 'none';
        fileName.textContent = '';
        fileSize.textContent = '';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Enhanced Date/Time Selection
    const dueDateSelect = document.getElementById('due_date');
    const dueTimeSelect = document.getElementById('due_time');

    if (dueDateSelect && dueTimeSelect) {
        // Create preview area
        const previewDiv = document.createElement('div');
        previewDiv.className = 'date-time-preview';
        previewDiv.innerHTML = `
            <div class="preview-label">📅 Assignment Due:</div>
            <div class="preview-datetime" id="datetime-preview">Please select date and time</div>
        `;

        // Insert preview after time selector
        const timeGroup = dueTimeSelect.closest('.form-group');
        timeGroup.parentNode.insertBefore(previewDiv, timeGroup.nextSibling);

        // Update preview when selections change
        function updatePreview() {
            const selectedDate = dueDateSelect.value;
            const selectedTime = dueTimeSelect.value;
            const previewElement = document.getElementById('datetime-preview');

            if (selectedDate && selectedTime) {
                // Parse and format the datetime
                const date = new Date(selectedDate + 'T' + selectedTime);
                const options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                };

                const formattedDateTime = date.toLocaleDateString('en-US', options);
                previewElement.textContent = formattedDateTime;
                previewDiv.classList.add('show');

                // Add urgency indicator
                const now = new Date();
                const timeDiff = date - now;
                const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

                let urgencyClass = '';
                let urgencyText = '';

                if (daysDiff < 1) {
                    urgencyClass = 'urgent';
                    urgencyText = ' ⚠️ Due today or overdue!';
                } else if (daysDiff <= 3) {
                    urgencyClass = 'soon';
                    urgencyText = ` ⏰ Due in ${daysDiff} day${daysDiff > 1 ? 's' : ''}`;
                } else if (daysDiff <= 7) {
                    urgencyClass = 'week';
                    urgencyText = ` 📅 Due in ${daysDiff} days`;
                } else {
                    urgencyClass = 'future';
                    urgencyText = ` 📅 Due in ${daysDiff} days`;
                }

                previewElement.className = `preview-datetime ${urgencyClass}`;
                previewElement.textContent = formattedDateTime + urgencyText;
            } else {
                previewElement.textContent = 'Please select date and time';
                previewDiv.classList.remove('show');
            }
        }

        // Add event listeners
        dueDateSelect.addEventListener('change', updatePreview);
        dueTimeSelect.addEventListener('change', updatePreview);

        // Set default time if not selected
        if (!dueTimeSelect.value) {
            dueTimeSelect.value = '23:59'; // Default to end of day
            updatePreview();
        }
    }
});
</script>

<style>
.preview-datetime.urgent {
    color: #dc3545;
    font-weight: bold;
}

.preview-datetime.soon {
    color: #fd7e14;
    font-weight: bold;
}

.preview-datetime.week {
    color: #ffc107;
}

.preview-datetime.future {
    color: #28a745;
}
</style>
{% endblock %}
