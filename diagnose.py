#!/usr/bin/env python3
"""
Diagnostic script to check what's causing the server startup issues.
"""

import sys
import os

def check_imports():
    """Check if all required modules can be imported."""
    print("🔍 Checking imports...")
    
    try:
        import flask
        print(f"  ✅ Flask: {flask.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print(f"  ✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-SQLAlchemy: {e}")
        return False
    
    try:
        import flask_login
        print(f"  ✅ Flask-Login: {flask_login.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-Login: {e}")
        return False
    
    try:
        import flask_wtf
        print(f"  ✅ Flask-WTF: {flask_wtf.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-WTF: {e}")
        return False
    
    return True

def check_files():
    """Check if required files exist."""
    print("\n📁 Checking files...")
    
    required_files = [
        'app.py',
        'models.py',
        'blueprints/auth.py',
        'blueprints/admin.py',
        'blueprints/teacher.py',
        'blueprints/student.py',
        'blueprints/parent.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - Missing!")
            all_exist = False
    
    return all_exist

def check_database():
    """Check database connectivity."""
    print("\n🗄️ Checking database...")
    
    try:
        import sqlite3
        db_path = os.path.join('instance', 'database.db')
        
        if os.path.exists(db_path):
            print(f"  ✅ Database file exists: {db_path}")
            
            # Try to connect
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"  ✅ Found {len(tables)} tables: {[t[0] for t in tables]}")
            
            conn.close()
            return True
        else:
            print(f"  ❌ Database file missing: {db_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ Database error: {e}")
        return False

def test_app_creation():
    """Test creating the Flask app."""
    print("\n🚀 Testing app creation...")
    
    try:
        from app import create_app
        print("  ✅ App module imported")
        
        app = create_app()
        print("  ✅ App created successfully")
        
        print(f"  ✅ App name: {app.name}")
        print(f"  ✅ Debug mode: {app.debug}")
        
        # Test a simple route
        with app.test_client() as client:
            response = client.get('/health')
            print(f"  ✅ Health check: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ App creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic checks."""
    print("🔧 Flask School Management System - Diagnostic Tool")
    print("=" * 60)
    
    checks = [
        ("Import Check", check_imports),
        ("File Check", check_files),
        ("Database Check", check_database),
        ("App Creation Check", test_app_creation)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"  ❌ {check_name} failed with exception: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ All diagnostic checks passed!")
        print("\n🚀 Try starting the server with: python run_server.py")
    else:
        print("❌ Some diagnostic checks failed!")
        print("\n💡 Please fix the issues above before starting the server.")

if __name__ == "__main__":
    main()
