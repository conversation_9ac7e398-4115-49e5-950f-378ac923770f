#!/usr/bin/env python3
"""
Test script to verify the optional datetime field fix.
"""

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://127.0.0.1:5000"

def get_csrf_token(session, url):
    """Extract CSRF token from a form page."""
    try:
        response = session.get(url, timeout=10)
        if response.status_code != 200:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        return csrf_input['value'] if csrf_input else None
    except:
        return None

def login_as_admin():
    """Login as admin and return session."""
    session = requests.Session()
    
    # Get CSRF token
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        print("❌ Could not get CSRF token")
        return None
    
    # Login
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    if response.status_code in [302, 303]:
        print("✅ Admin login successful")
        return session
    else:
        print(f"❌ Admin login failed: {response.status_code}")
        return None

def test_notification_creation_without_expiry():
    """Test creating notification with empty expiration date."""
    print("🧪 Testing notification creation with empty expiration date...")
    
    session = login_as_admin()
    if not session:
        return False
    
    # Get CSRF token for notification creation
    csrf_token = get_csrf_token(session, f"{BASE_URL}/admin/notifications/create")
    if not csrf_token:
        print("❌ Could not get CSRF token for notification creation")
        return False
    
    # Create notification with empty expiration date
    notification_data = {
        'title': 'Test Notification - No Expiry',
        'message': 'This notification should be created successfully without an expiration date.',
        'notification_type': 'info',
        'recipient_role': 'all',
        'expires_at': '',  # Empty expiration date
        'csrf_token': csrf_token
    }
    
    print("📝 Submitting notification with empty expiration date...")
    response = session.post(f"{BASE_URL}/admin/notifications/create", data=notification_data, allow_redirects=False)
    
    if response.status_code in [302, 303]:
        print("✅ SUCCESS: Notification created with empty expiration date!")
        
        # Verify it appears in the notification list
        list_response = session.get(f"{BASE_URL}/admin/notifications")
        if list_response.status_code == 200:
            soup = BeautifulSoup(list_response.content, 'html.parser')
            notification_cards = soup.find_all('div', class_='notification-card')
            
            # Look for our test notification
            found = False
            for card in notification_cards:
                title_elem = card.find('h3')
                if title_elem and 'Test Notification - No Expiry' in title_elem.get_text():
                    found = True
                    print("✅ Notification appears in admin list")
                    break
            
            if not found:
                print("⚠️ Notification created but not found in list")
        
        return True
    else:
        print(f"❌ FAILED: Notification creation failed with status {response.status_code}")
        
        # Check for validation errors
        soup = BeautifulSoup(response.content, 'html.parser')
        error_divs = soup.find_all('div', class_='form-error')
        if error_divs:
            print("Validation errors found:")
            for error_div in error_divs:
                error_text = error_div.get_text().strip()
                print(f"  - {error_text}")
        
        return False

def test_notification_creation_with_expiry():
    """Test creating notification with valid expiration date."""
    print("\n🧪 Testing notification creation with valid expiration date...")
    
    session = login_as_admin()
    if not session:
        return False
    
    # Get CSRF token for notification creation
    csrf_token = get_csrf_token(session, f"{BASE_URL}/admin/notifications/create")
    if not csrf_token:
        print("❌ Could not get CSRF token for notification creation")
        return False
    
    # Create notification with valid expiration date
    notification_data = {
        'title': 'Test Notification - With Expiry',
        'message': 'This notification has an expiration date.',
        'notification_type': 'warning',
        'recipient_role': 'teacher',
        'expires_at': '2024-12-31T23:59',  # Valid expiration date
        'csrf_token': csrf_token
    }
    
    print("📝 Submitting notification with valid expiration date...")
    response = session.post(f"{BASE_URL}/admin/notifications/create", data=notification_data, allow_redirects=False)
    
    if response.status_code in [302, 303]:
        print("✅ SUCCESS: Notification created with expiration date!")
        return True
    else:
        print(f"❌ FAILED: Notification creation failed with status {response.status_code}")
        return False

def main():
    """Run the optional datetime field test."""
    print("🔧 Optional DateTime Field Fix Test")
    print("=" * 40)
    
    # Test 1: Empty expiration date (should work now)
    test1_result = test_notification_creation_without_expiry()
    
    # Test 2: Valid expiration date (should still work)
    test2_result = test_notification_creation_with_expiry()
    
    print("\n" + "=" * 40)
    print("📊 TEST RESULTS")
    print("=" * 40)
    print(f"Empty expiration date: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Valid expiration date: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED - Optional datetime field is working correctly!")
        return True
    else:
        print("\n⚠️ Some tests failed - check the issues above")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
