#!/usr/bin/env python3
"""
Comprehensive End-to-End Test Suite for Flask School Management System
=====================================================================
Tests all Phase 3 features and functionality systematically.
"""

import requests
import sys
import time
from bs4 import BeautifulSoup
import json

BASE_URL = "http://127.0.0.1:5000"

class TestResults:
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"  ✅ {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"  ❌ {test_name}: {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n📊 Test Results: {self.passed}/{total} passed, {self.failed} failed")
        if self.errors:
            print("\n❌ Failed Tests:")
            for error in self.errors:
                print(f"  - {error}")

def test_server_startup():
    """Test 1: Server Startup Verification"""
    print("🚀 Test 1: Server Startup Verification")
    results = TestResults()
    
    try:
        # Test basic connectivity
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            results.add_pass("Server responds to health check")
        else:
            results.add_fail("Health check", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Server connectivity", str(e))
        return results
    
    try:
        # Test home page
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            results.add_pass("Home page accessible")
        else:
            results.add_fail("Home page", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Home page", str(e))
    
    try:
        # Test route registration by checking debug endpoint
        response = requests.get(f"{BASE_URL}/debug/routes", timeout=10)
        if response.status_code == 200:
            routes_data = response.json()
            total_routes = len(routes_data)
            
            # Check for Phase 3 specific routes
            phase3_routes = [
                '/admin/overview',
                '/admin/notifications',
                '/admin/notifications/create',
                '/teacher/classes/manage',
                '/teacher/submission/<int:submission_id>/grade'
            ]
            
            found_routes = 0
            for route in phase3_routes:
                if any(route in r for r in routes_data):
                    found_routes += 1
            
            results.add_pass(f"Route registration ({total_routes} total routes)")
            results.add_pass(f"Phase 3 routes ({found_routes}/{len(phase3_routes)} found)")
        else:
            results.add_fail("Route registration check", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Route registration", str(e))
    
    return results

def get_csrf_token(session, url):
    """Helper function to extract CSRF token from a form page."""
    try:
        response = session.get(url, timeout=10)
        if response.status_code != 200:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        return csrf_input['value'] if csrf_input else None
    except:
        return None

def test_authentication():
    """Test 2: Authentication Testing"""
    print("\n🔐 Test 2: Authentication Testing")
    results = TestResults()
    
    test_credentials = [
        ('admin', 'Admin123!', 'admin'),
        ('teacher1', 'Teacher123!', 'teacher'),
        ('student1', 'Student123!', 'student'),
        ('parent1', 'Parent123!', 'parent')
    ]
    
    for username, password, expected_role in test_credentials:
        session = requests.Session()
        try:
            # Get login page and CSRF token
            csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
            if not csrf_token:
                results.add_fail(f"{expected_role} login", "Could not get CSRF token")
                continue
            
            # Attempt login
            login_data = {
                'username': username,
                'password': password,
                'csrf_token': csrf_token
            }
            
            response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
            
            if response.status_code in [302, 303]:
                # Check if redirected to appropriate dashboard
                dashboard_response = session.get(f"{BASE_URL}/{expected_role}/dashboard")
                if dashboard_response.status_code == 200:
                    results.add_pass(f"{expected_role.title()} login and dashboard access")
                else:
                    results.add_fail(f"{expected_role} dashboard", f"Status {dashboard_response.status_code}")
            else:
                results.add_fail(f"{expected_role} login", f"Login failed - Status {response.status_code}")
                
        except Exception as e:
            results.add_fail(f"{expected_role} authentication", str(e))
    
    return results

def test_admin_features():
    """Test 3: Admin Phase 3 Features"""
    print("\n👑 Test 3: Admin Phase 3 Features")
    results = TestResults()
    
    # Login as admin
    session = requests.Session()
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        results.add_fail("Admin login setup", "Could not get CSRF token")
        return results
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'csrf_token': csrf_token
    }
    
    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    if login_response.status_code not in [302, 303]:
        results.add_fail("Admin login", "Login failed")
        return results
    
    # Test System Overview
    try:
        response = session.get(f"{BASE_URL}/admin/overview")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for key elements
            stat_cards = soup.find_all('div', class_='stat-card')
            grade_chart = soup.find('div', class_='grade-chart')
            submissions_table = soup.find('table', class_='table')
            
            if len(stat_cards) >= 4:
                results.add_pass("System Overview - Statistics cards")
            else:
                results.add_fail("System Overview", f"Only {len(stat_cards)} stat cards found")
            
            if grade_chart:
                results.add_pass("System Overview - Grade distribution chart")
            else:
                results.add_fail("System Overview", "Grade chart missing")
            
            if submissions_table:
                results.add_pass("System Overview - Recent submissions table")
            else:
                results.add_pass("System Overview - No submissions yet (expected)")
                
        else:
            results.add_fail("System Overview access", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("System Overview", str(e))
    
    # Test Notification Management
    try:
        response = session.get(f"{BASE_URL}/admin/notifications")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for notification management elements
            create_button = soup.find('a', href=lambda x: x and 'create' in x and 'notification' in x)
            notification_cards = soup.find_all('div', class_='notification-card')

            if create_button:
                results.add_pass("Notification Management - Create button")
            else:
                results.add_fail("Notification Management", "Create button missing")
            
            results.add_pass(f"Notification Management - Interface ({len(notification_cards)} notifications)")
            
        else:
            results.add_fail("Notification Management access", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Notification Management", str(e))
    
    # Test Notification Creation
    try:
        response = session.get(f"{BASE_URL}/admin/notifications/create")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for form elements
            title_field = soup.find('input', {'name': 'title'})
            message_field = soup.find('textarea', {'name': 'message'})
            type_field = soup.find('select', {'name': 'notification_type'})
            role_field = soup.find('select', {'name': 'recipient_role'})
            
            if all([title_field, message_field, type_field, role_field]):
                results.add_pass("Notification Creation - Form complete")
            else:
                results.add_fail("Notification Creation", "Form incomplete")
                
        else:
            results.add_fail("Notification Creation access", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Notification Creation", str(e))
    
    return results

def test_teacher_features():
    """Test 4: Teacher Phase 3 Features"""
    print("\n👨‍🏫 Test 4: Teacher Phase 3 Features")
    results = TestResults()
    
    # Login as teacher
    session = requests.Session()
    csrf_token = get_csrf_token(session, f"{BASE_URL}/login")
    if not csrf_token:
        results.add_fail("Teacher login setup", "Could not get CSRF token")
        return results
    
    login_data = {
        'username': 'teacher1',
        'password': 'Teacher123!',
        'csrf_token': csrf_token
    }
    
    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    if login_response.status_code not in [302, 303]:
        results.add_fail("Teacher login", "Login failed")
        return results
    
    # Test Class Management
    try:
        response = session.get(f"{BASE_URL}/teacher/classes/manage")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for class management elements
            stat_cards = soup.find_all('div', class_='stat-card')
            class_cards = soup.find_all('div', class_='class-card')
            empty_state = soup.find('div', class_='empty-state')
            
            if stat_cards or empty_state:
                results.add_pass("Class Management - Interface loads")
            else:
                results.add_fail("Class Management", "Interface missing")
            
            if class_cards:
                results.add_pass(f"Class Management - {len(class_cards)} classes found")
            else:
                results.add_pass("Class Management - No classes yet (expected)")
                
        else:
            results.add_fail("Class Management access", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Class Management", str(e))
    
    # Test Enhanced Assignment Center
    try:
        response = session.get(f"{BASE_URL}/teacher/assignments")
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for assignment center elements
            assignment_cards = soup.find_all('div', class_='assignment-card')
            create_button = soup.find('a', href=lambda x: x and 'create' in x)
            empty_state = soup.find('div', class_='empty-state')
            
            if create_button:
                results.add_pass("Assignment Center - Create button")
            else:
                results.add_fail("Assignment Center", "Create button missing")
            
            if assignment_cards:
                results.add_pass(f"Assignment Center - {len(assignment_cards)} assignments found")
            else:
                results.add_pass("Assignment Center - No assignments yet (expected)")
                
        else:
            results.add_fail("Assignment Center access", f"Status {response.status_code}")
    except Exception as e:
        results.add_fail("Assignment Center", str(e))
    
    return results

def main():
    """Run comprehensive test suite"""
    print("🧪 Flask School Management System - Comprehensive Test Suite")
    print("=" * 70)
    print("Testing Phase 3 Enhanced Features")
    print("=" * 70)
    
    all_results = []
    
    # Run all test suites
    all_results.append(test_server_startup())
    all_results.append(test_authentication())
    all_results.append(test_admin_features())
    all_results.append(test_teacher_features())
    
    # Calculate overall results
    total_passed = sum(r.passed for r in all_results)
    total_failed = sum(r.failed for r in all_results)
    total_tests = total_passed + total_failed
    
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {total_passed}")
    print(f"Failed: {total_failed}")
    print(f"Success Rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "0%")
    
    if total_failed > 0:
        print("\n❌ FAILED TESTS:")
        for result in all_results:
            for error in result.errors:
                print(f"  - {error}")
    
    if total_failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Phase 3 implementation is working correctly")
        print("\n🌐 Ready for production use!")
    else:
        print(f"\n⚠️  {total_failed} tests failed - review issues above")
    
    return total_failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
