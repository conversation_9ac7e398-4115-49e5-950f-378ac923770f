{% extends "base.html" %}

{% block title %}Grade Submission - Teacher Dashboard{% endblock %}

{% block content %}
<div class="grading-container">
    <div class="grading-header">
        <h1>📝 Grade Submission</h1>
        <div class="breadcrumb">
            <a href="{{ url_for('teacher.dashboard') }}">Dashboard</a> > 
            <a href="{{ url_for('teacher.assignments') }}">Assignments</a> > 
            <a href="{{ url_for('teacher.assignment_detail', assignment_id=submission.assignment.id) }}">{{ submission.assignment.title }}</a> > 
            Grade Submission
        </div>
    </div>

    <div class="grading-content">
        <div class="submission-info-card">
            <div class="student-info">
                <div class="student-avatar">👨‍🎓</div>
                <div class="student-details">
                    <h3>{{ submission.student.first_name }} {{ submission.student.last_name }}</h3>
                    <p class="student-meta">Grade {{ submission.student.grade }} • Student ID: {{ submission.student.id }}</p>
                </div>
            </div>
            
            <div class="assignment-info">
                <h4>{{ submission.assignment.title }}</h4>
                <p><strong>Subject:</strong> {{ submission.assignment.class_.subject }}</p>
                <p><strong>Due Date:</strong> {{ submission.assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}</p>
                <p><strong>Submitted:</strong> {{ submission.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                {% if submission.submitted_at > submission.assignment.due_date %}
                    <span class="late-badge">⚠️ Late Submission</span>
                {% endif %}
            </div>
        </div>

        <div class="submission-content">
            {% if submission.file_path %}
                <div class="file-section">
                    <h4>📎 Submitted File</h4>
                    <div class="file-info">
                        <div class="file-details">
                            <span class="file-name">{{ submission.file_path.split('_')[-1] }}</span>
                            <span class="file-type">{{ submission.file_path.split('.')[-1].upper() }}</span>
                        </div>
                        <div class="file-actions">
                            <a href="{{ url_for('download_file', filename=submission.file_path) }}" class="btn btn-outline-primary btn-sm">
                                📥 Download File
                            </a>
                            {% if submission.file_path.endswith('.pdf') %}
                                <button class="btn btn-outline-secondary btn-sm" onclick="previewFile()">
                                    👁️ Preview
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grading-form-section">
                <h4>✏️ Grade & Feedback</h4>
                <form method="POST" class="grading-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="form-row">
                        <div class="form-group grade-input">
                            {{ form.grade.label(class="form-label") }}
                            <div class="grade-input-wrapper">
                                {{ form.grade(class="form-control grade-field", placeholder="0-100") }}
                                <span class="grade-suffix">%</span>
                            </div>
                            {% if form.grade.errors %}
                                <div class="form-error">
                                    {% for error in form.grade.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="grade-scale">
                                <span class="scale-item" data-grade="90">A (90-100)</span>
                                <span class="scale-item" data-grade="80">B (80-89)</span>
                                <span class="scale-item" data-grade="70">C (70-79)</span>
                                <span class="scale-item" data-grade="60">D (60-69)</span>
                                <span class="scale-item" data-grade="50">F (0-59)</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        {{ form.feedback.label(class="form-label") }}
                        {{ form.feedback(class="form-control feedback-field", rows="6") }}
                        {% if form.feedback.errors %}
                            <div class="form-error">
                                {% for error in form.feedback.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-help">Provide constructive feedback to help the student improve</small>
                    </div>

                    <div class="form-actions">
                        <a href="{{ url_for('teacher.assignment_detail', assignment_id=submission.assignment.id) }}" class="btn btn-secondary">Cancel</a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.grading-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2em;
}

.grading-header {
    margin-bottom: 2em;
}

.grading-header h1 {
    color: var(--primary);
    margin-bottom: 0.5em;
}

.breadcrumb {
    color: var(--secondary);
    font-size: 0.9em;
}

.breadcrumb a {
    color: var(--primary);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.grading-content {
    display: grid;
    gap: 2em;
}

.submission-info-card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.student-info {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1.5em;
    padding-bottom: 1em;
    border-bottom: 1px solid var(--border);
}

.student-avatar {
    font-size: 3em;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-details h3 {
    margin: 0;
    color: var(--text);
}

.student-meta {
    margin: 0.25em 0 0 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.assignment-info h4 {
    color: var(--primary);
    margin-bottom: 0.5em;
}

.assignment-info p {
    margin: 0.25em 0;
    color: var(--text);
}

.late-badge {
    background: #fff3cd;
    color: #856404;
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.submission-content {
    display: grid;
    gap: 2em;
}

.file-section, .grading-form-section {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border);
}

.file-section h4, .grading-form-section h4 {
    color: var(--primary);
    margin-bottom: 1em;
}

.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1em;
    background: rgba(0,0,0,0.02);
    border-radius: 6px;
    border: 1px solid var(--border);
}

.file-name {
    font-weight: 500;
    color: var(--text);
}

.file-type {
    background: var(--primary);
    color: white;
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-size: 0.8em;
    margin-left: 0.5em;
}

.file-actions {
    display: flex;
    gap: 0.5em;
}

.grading-form {
    max-width: 600px;
}

.grade-input-wrapper {
    position: relative;
    display: inline-block;
    width: 120px;
}

.grade-field {
    width: 100%;
    padding-right: 30px;
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
}

.grade-suffix {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary);
    font-weight: bold;
}

.grade-scale {
    display: flex;
    gap: 0.5em;
    margin-top: 0.5em;
    flex-wrap: wrap;
}

.scale-item {
    background: rgba(0,0,0,0.05);
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-size: 0.8em;
    cursor: pointer;
    transition: background 0.2s ease;
}

.scale-item:hover {
    background: var(--primary);
    color: white;
}

.feedback-field {
    min-height: 120px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 1em;
    justify-content: flex-end;
    margin-top: 2em;
}

@media (max-width: 768px) {
    .grading-container {
        padding: 1em;
    }
    
    .student-info {
        flex-direction: column;
        text-align: center;
    }
    
    .file-info {
        flex-direction: column;
        gap: 1em;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Grade scale quick selection
    const gradeField = document.querySelector('.grade-field');
    const scaleItems = document.querySelectorAll('.scale-item');
    
    scaleItems.forEach(item => {
        item.addEventListener('click', function() {
            const grade = this.getAttribute('data-grade');
            gradeField.value = grade;
            gradeField.focus();
        });
    });
    
    // Grade validation and color coding
    gradeField.addEventListener('input', function() {
        const grade = parseInt(this.value);
        this.style.color = getGradeColor(grade);
    });
    
    function getGradeColor(grade) {
        if (grade >= 90) return '#28a745';
        if (grade >= 80) return '#17a2b8';
        if (grade >= 70) return '#ffc107';
        if (grade >= 60) return '#fd7e14';
        return '#dc3545';
    }
});

function previewFile() {
    // This would open a file preview modal or new window
    alert('File preview functionality would be implemented here');
}
</script>
{% endblock %}
