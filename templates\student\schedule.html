{% extends "base.html" %}

{% block title %}Class Schedule - Student Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>📅 Class Schedule</h1>
        <div class="student-info">
            <p><strong>Student:</strong> {{ student.first_name }} {{ student.last_name }}</p>
            <p><strong>Grade:</strong> {{ student.grade }}</p>
        </div>
    </div>

    <div class="schedule-container">
        {% if schedule %}
            <div class="schedule-grid">
                {% set days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] %}
                {% for day in days %}
                    <div class="day-column">
                        <div class="day-header">
                            <h3>{{ day }}</h3>
                        </div>
                        <div class="day-classes">
                            {% if schedule.get(day) %}
                                {% for class in schedule[day] %}
                                    <div class="class-card">
                                        <div class="class-time">{{ class.time }}</div>
                                        <div class="class-subject">{{ class.subject }}</div>
                                        <div class="class-teacher">{{ class.teacher }}</div>
                                        <div class="class-room">Room {{ class.room }}</div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="no-classes">
                                    <p>No classes scheduled</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h3>No Classes Enrolled</h3>
                <p>You are not currently enrolled in any classes. Contact your administrator for enrollment.</p>
            </div>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
        <a href="{{ url_for('student.assignments') }}" class="btn btn-primary">View Assignments</a>
    </div>
</div>

<style>
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 2px solid var(--border);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.student-info {
    text-align: right;
    color: var(--secondary);
}

.student-info p {
    margin: 0.25em 0;
}

.schedule-container {
    margin-bottom: 2em;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1em;
    margin-bottom: 2em;
}

.day-column {
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.day-header {
    background: var(--primary);
    color: white;
    padding: 1em;
    text-align: center;
}

.day-header h3 {
    margin: 0;
    font-size: 1.1em;
}

.day-classes {
    padding: 1em;
    min-height: 300px;
}

.class-card {
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    border-radius: 6px;
    padding: 0.75em;
    margin-bottom: 0.75em;
    transition: transform 0.2s ease;
}

.class-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.class-time {
    font-weight: bold;
    color: var(--primary);
    font-size: 1.1em;
    margin-bottom: 0.25em;
}

.class-subject {
    font-weight: bold;
    color: var(--text);
    margin-bottom: 0.25em;
}

.class-teacher {
    color: var(--secondary);
    font-size: 0.9em;
    margin-bottom: 0.25em;
}

.class-room {
    color: var(--muted);
    font-size: 0.85em;
    font-style: italic;
}

.no-classes {
    text-align: center;
    color: var(--muted);
    padding: 2em 0;
}

.empty-state {
    text-align: center;
    padding: 3em;
    color: var(--secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: 0.5em;
}

.dashboard-actions {
    display: flex;
    gap: 1em;
    justify-content: center;
    padding-top: 2em;
    border-top: 1px solid var(--border);
}

@media (max-width: 1024px) {
    .schedule-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 1em;
    }
    
    .student-info {
        text-align: center;
    }
    
    .schedule-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .schedule-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-container {
        padding: 1em;
    }
}
</style>
{% endblock %}
