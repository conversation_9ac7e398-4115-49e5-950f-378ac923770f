#!/usr/bin/env python3
"""
Fresh restart diagnostic to identify and fix all issues.
"""

import sys
import traceback

def test_basic_imports():
    """Test basic imports first."""
    print("🔍 Testing Basic Imports...")
    
    try:
        import flask
        print(f"  ✅ Flask: {flask.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print(f"  ✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-SQLAlchemy: {e}")
        return False
    
    try:
        import flask_login
        print(f"  ✅ Flask-Login: {flask_login.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-Login: {e}")
        return False
    
    try:
        import flask_wtf
        print(f"  ✅ Flask-WTF: {flask_wtf.__version__}")
    except ImportError as e:
        print(f"  ❌ Flask-WTF: {e}")
        return False
    
    return True

def test_config_import():
    """Test config import."""
    print("\n⚙️ Testing Config Import...")
    
    try:
        from config import config
        print("  ✅ Config imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ Config import failed: {e}")
        traceback.print_exc()
        return False

def test_db_import():
    """Test database import."""
    print("\n🗄️ Testing Database Import...")
    
    try:
        from db import db
        print("  ✅ Database module imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ Database import failed: {e}")
        traceback.print_exc()
        return False

def test_models_import():
    """Test models import."""
    print("\n📊 Testing Models Import...")
    
    try:
        from models import User, Student, Teacher, Parent, Class, Assignment, Submission, Notification, ClassEnrollment
        print("  ✅ All models imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ Models import failed: {e}")
        traceback.print_exc()
        return False

def test_blueprints_import():
    """Test blueprint imports individually."""
    print("\n🔗 Testing Blueprint Imports...")
    
    blueprints = [
        ('auth', 'blueprints.auth'),
        ('admin', 'blueprints.admin'),
        ('teacher', 'blueprints.teacher'),
        ('student', 'blueprints.student'),
        ('parent', 'blueprints.parent')
    ]
    
    for name, module in blueprints:
        try:
            imported_module = __import__(module, fromlist=[name])
            blueprint = getattr(imported_module, name)
            print(f"  ✅ {name} blueprint imported successfully")
        except Exception as e:
            print(f"  ❌ {name} blueprint import failed: {e}")
            traceback.print_exc()
            return False
    
    return True

def test_app_creation():
    """Test Flask app creation step by step."""
    print("\n🚀 Testing App Creation...")
    
    try:
        from app import create_app
        print("  ✅ create_app function imported")
        
        app = create_app()
        print("  ✅ Flask app created successfully")
        
        # Test app context
        with app.app_context():
            print("  ✅ App context works")
            
            # Test database in context
            from db import db
            print("  ✅ Database accessible in app context")
            
            # Count routes
            routes = list(app.url_map.iter_rules())
            print(f"  ✅ {len(routes)} routes registered")
            
            # Check for key routes
            route_strings = [str(rule) for rule in routes]
            key_routes = ['/login', '/admin/dashboard', '/teacher/dashboard']
            
            for route in key_routes:
                found = any(route in r for r in route_strings)
                print(f"    {'✅' if found else '❌'} {route}")
        
        return True
    except Exception as e:
        print(f"  ❌ App creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run restart diagnostic."""
    print("🔄 Flask School Management System - Restart Diagnostic")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Config Import", test_config_import),
        ("Database Import", test_db_import),
        ("Models Import", test_models_import),
        ("Blueprints Import", test_blueprints_import),
        ("App Creation", test_app_creation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
                print(f"\n❌ {test_name} failed - stopping here")
                break
        except Exception as e:
            print(f"\n❌ {test_name} crashed: {e}")
            failed += 1
            break
    
    print("\n" + "=" * 60)
    print(f"📊 Diagnostic Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✅ All tests passed! Ready to start server.")
        return True
    else:
        print("❌ Issues found. Fix the problems above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
