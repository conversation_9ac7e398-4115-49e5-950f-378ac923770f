#!/usr/bin/env python3
"""
Simple server to test if Flask is working at all.
"""

print("🔍 Testing Flask installation...")

try:
    from flask import Flask, render_template_string
    print("✅ Flask imported successfully")
except ImportError as e:
    print(f"❌ Flask import failed: {e}")
    exit(1)

print("🚀 Creating Flask app...")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

@app.route('/')
def home():
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Flask School Management System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #007bff; }
            .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .links { margin: 20px 0; }
            .links a { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            .links a:hover { background: #0056b3; }
            .credentials { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 Flask School Management System</h1>
            
            <div class="status">
                ✅ <strong>Server Status:</strong> Running successfully on http://127.0.0.1:5000
            </div>
            
            <h2>🧪 Test Links</h2>
            <div class="links">
                <a href="/health">Health Check</a>
                <a href="/test">Test Route</a>
                <a href="/info">System Info</a>
            </div>
            
            <div class="credentials">
                <h3>🔑 Test Credentials (for full app):</h3>
                <ul>
                    <li><strong>Admin:</strong> admin / Admin123!</li>
                    <li><strong>Teacher:</strong> teacher1 / Teacher123!</li>
                    <li><strong>Student:</strong> student1 / Student123!</li>
                    <li><strong>Parent:</strong> parent1 / Parent123!</li>
                </ul>
            </div>
            
            <h2>📋 Next Steps</h2>
            <p>If you can see this page, Flask is working correctly. The issue might be with the full application.</p>
            <p>Try running the full application with: <code>python run_server.py</code></p>
        </div>
    </body>
    </html>
    ''')

@app.route('/health')
def health():
    return {'status': 'ok', 'message': 'Flask server is running', 'version': '1.0'}

@app.route('/test')
def test():
    return render_template_string('''
    <h1>✅ Test Route Working!</h1>
    <p>Flask routing is functioning correctly.</p>
    <a href="/">← Back to Home</a>
    ''')

@app.route('/info')
def info():
    import sys
    import os
    return render_template_string('''
    <h1>📊 System Information</h1>
    <ul>
        <li><strong>Python Version:</strong> {{ python_version }}</li>
        <li><strong>Flask Version:</strong> {{ flask_version }}</li>
        <li><strong>Working Directory:</strong> {{ working_dir }}</li>
        <li><strong>Platform:</strong> {{ platform }}</li>
    </ul>
    <a href="/">← Back to Home</a>
    ''', 
    python_version=sys.version,
    flask_version=Flask.__version__ if hasattr(Flask, '__version__') else 'Unknown',
    working_dir=os.getcwd(),
    platform=sys.platform
    )

if __name__ == '__main__':
    print("✅ Flask app created successfully")
    print("🌐 Starting server at: http://127.0.0.1:5000")
    print("📱 Open the URL in your browser to test")
    print("⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        import traceback
        traceback.print_exc()
